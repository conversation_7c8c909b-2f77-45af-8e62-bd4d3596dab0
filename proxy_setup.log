2025-07-15 22:40:13,421 - ProxySetup - INFO - Starting proxy setup...
2025-07-15 22:40:13,421 - ProxySetup - INFO - Setting up proxy configuration...
2025-07-15 22:40:13,421 - ConfigManager - INFO - Configuration file not found, creating default
2025-07-15 22:40:13,422 - ConfigManager - INFO - Configuration saved successfully
2025-07-15 22:40:13,423 - Config<PERSON>anager - INFO - Configuration saved successfully
2025-07-15 22:40:13,424 - ConfigManager - INFO - Configuration saved successfully
2025-07-15 22:40:13,425 - ConfigManager - INFO - Configuration saved successfully
2025-07-15 22:40:13,426 - ConfigManager - INFO - Configuration saved successfully
2025-07-15 22:40:13,427 - ConfigManager - INFO - Configuration saved successfully
2025-07-15 22:40:13,427 - Config<PERSON>anager - INFO - Proxy configuration updated from string
2025-07-15 22:40:13,428 - Config<PERSON>anager - INFO - Configuration saved successfully
2025-07-15 22:40:13,429 - Config<PERSON>anager - INFO - Configuration saved successfully
2025-07-15 22:40:13,430 - ConfigManager - INFO - Configuration saved successfully
2025-07-15 22:40:13,430 - ConfigManager - INFO - Configuration saved successfully
2025-07-15 22:40:13,431 - ConfigManager - INFO - Configuration saved successfully
2025-07-15 22:40:13,431 - ProxySetup - INFO - Initializing proxy manager...
2025-07-15 22:40:13,432 - ProxyManager - INFO - Proxy disabled in configuration
2025-07-15 22:40:13,432 - ProxySetup - INFO - Testing proxy connection...
2025-07-15 22:40:13,432 - ProxySetup - INFO - Testing proxy connection synchronously...
2025-07-15 22:40:13,435 - ProxySetup - INFO - The script will fall back to direct connection when proxy fails
2025-07-15 22:40:13,435 - ProxySetup - INFO - Creating legacy settings file...
2025-07-15 22:40:13,438 - ConfigManager - INFO - Configuration validation passed
2025-07-15 22:40:13,440 - ProxySetup - INFO - You can now run your groups.py script with enhanced proxy support
2025-07-15 22:41:11,754 - ProxySetup - INFO - Starting proxy setup...
2025-07-15 22:41:11,754 - ProxySetup - INFO - Setting up proxy configuration...
2025-07-15 22:41:11,754 - ConfigManager - INFO - Configuration loaded successfully
2025-07-15 22:41:11,754 - ConfigManager - INFO - Configuration saved successfully
2025-07-15 22:41:11,754 - ConfigManager - INFO - Configuration saved successfully
2025-07-15 22:41:11,754 - ConfigManager - INFO - Configuration saved successfully
2025-07-15 22:41:11,754 - ConfigManager - INFO - Configuration saved successfully
2025-07-15 22:41:11,754 - ConfigManager - INFO - Configuration saved successfully
2025-07-15 22:41:11,754 - ConfigManager - INFO - Proxy configuration updated from string
2025-07-15 22:41:11,754 - ConfigManager - INFO - Configuration saved successfully
2025-07-15 22:41:11,754 - ConfigManager - INFO - Configuration saved successfully
2025-07-15 22:41:11,754 - ConfigManager - INFO - Configuration saved successfully
2025-07-15 22:41:11,754 - ConfigManager - INFO - Configuration saved successfully
2025-07-15 22:41:11,754 - ConfigManager - INFO - Configuration saved successfully
2025-07-15 22:41:11,754 - ProxySetup - INFO - Initializing proxy manager...
2025-07-15 22:41:11,754 - ProxyManager - INFO - Proxy disabled in configuration
2025-07-15 22:41:11,754 - ProxySetup - INFO - Testing proxy connection...
2025-07-15 22:41:11,754 - ProxySetup - INFO - Testing proxy connection synchronously...
2025-07-15 22:41:11,769 - ProxySetup - INFO - The script will fall back to direct connection when proxy fails
2025-07-15 22:41:11,769 - ProxySetup - INFO - Creating legacy settings file...
2025-07-15 22:41:11,772 - ConfigManager - INFO - Configuration validation passed
2025-07-15 22:41:11,774 - ProxySetup - INFO - You can now run your groups.py script with enhanced proxy support
2025-07-15 22:41:52,583 - ProxySetup - INFO - Starting proxy setup...
2025-07-15 22:41:52,583 - ProxySetup - INFO - Setting up proxy configuration...
2025-07-15 22:41:52,583 - ConfigManager - INFO - Configuration loaded successfully
2025-07-15 22:41:52,583 - ConfigManager - INFO - Configuration saved successfully
2025-07-15 22:41:52,583 - ConfigManager - INFO - Configuration saved successfully
2025-07-15 22:41:52,583 - ConfigManager - INFO - Configuration saved successfully
2025-07-15 22:41:52,583 - ConfigManager - INFO - Configuration saved successfully
2025-07-15 22:41:52,583 - ConfigManager - INFO - Configuration saved successfully
2025-07-15 22:41:52,583 - ConfigManager - INFO - Proxy configuration updated from string
2025-07-15 22:41:52,583 - ConfigManager - INFO - Configuration saved successfully
2025-07-15 22:41:52,583 - ConfigManager - INFO - Configuration saved successfully
2025-07-15 22:41:52,583 - ConfigManager - INFO - Configuration saved successfully
2025-07-15 22:41:52,583 - ConfigManager - INFO - Configuration saved successfully
2025-07-15 22:41:52,583 - ConfigManager - INFO - Configuration saved successfully
2025-07-15 22:41:52,583 - ProxySetup - INFO - Initializing proxy manager...
2025-07-15 22:41:52,583 - ProxyManager - INFO - Proxy disabled in configuration
2025-07-15 22:41:52,583 - ProxySetup - INFO - Testing proxy connection...
2025-07-15 22:41:52,583 - ProxySetup - INFO - Testing proxy connection synchronously...
2025-07-15 22:41:52,595 - ProxySetup - INFO - The script will fall back to direct connection when proxy fails
2025-07-15 22:41:52,595 - ProxySetup - INFO - Creating legacy settings file...
2025-07-15 22:41:52,598 - ConfigManager - INFO - Configuration validation passed
2025-07-15 22:41:52,600 - ProxySetup - INFO - You can now run your groups.py script with enhanced proxy support
2025-07-15 22:42:42,793 - ProxySetup - INFO - Starting proxy setup...
2025-07-15 22:42:42,793 - ProxySetup - INFO - Setting up proxy configuration...
2025-07-15 22:42:42,793 - ConfigManager - INFO - Configuration loaded successfully
2025-07-15 22:42:42,793 - ConfigManager - INFO - Configuration saved successfully
2025-07-15 22:42:42,793 - ConfigManager - INFO - Configuration saved successfully
2025-07-15 22:42:42,793 - ConfigManager - INFO - Configuration saved successfully
2025-07-15 22:42:42,793 - ConfigManager - INFO - Configuration saved successfully
2025-07-15 22:42:42,793 - ConfigManager - INFO - Configuration saved successfully
2025-07-15 22:42:42,793 - ConfigManager - INFO - Proxy configuration updated from string
2025-07-15 22:42:42,793 - ConfigManager - INFO - Configuration saved successfully
2025-07-15 22:42:42,793 - ConfigManager - INFO - Configuration saved successfully
2025-07-15 22:42:42,793 - ConfigManager - INFO - Configuration saved successfully
2025-07-15 22:42:42,793 - ConfigManager - INFO - Configuration saved successfully
2025-07-15 22:42:42,793 - ConfigManager - INFO - Configuration saved successfully
2025-07-15 22:42:42,793 - ProxySetup - INFO - Initializing proxy manager...
2025-07-15 22:42:42,808 - ProxyManager - INFO - Proxy disabled in configuration
2025-07-15 22:42:42,808 - ProxySetup - INFO - Testing proxy connection...
2025-07-15 22:42:42,808 - ProxySetup - INFO - Testing proxy connection synchronously...
2025-07-15 22:42:42,812 - ProxySetup - INFO - The script will fall back to direct connection when proxy fails
2025-07-15 22:42:42,812 - ProxySetup - INFO - Creating legacy settings file...
2025-07-15 22:42:42,814 - ConfigManager - INFO - Configuration validation passed
2025-07-15 22:42:42,816 - ProxySetup - INFO - You can now run your groups.py script with enhanced proxy support
2025-07-15 22:45:16,649 - ProxySetup - INFO - Starting proxy setup...
2025-07-15 22:45:16,649 - ProxySetup - INFO - Setting up proxy configuration...
2025-07-15 22:45:16,649 - ConfigManager - INFO - Configuration loaded successfully
2025-07-15 22:45:16,650 - ConfigManager - INFO - Configuration saved successfully
2025-07-15 22:45:16,651 - ConfigManager - INFO - Configuration saved successfully
2025-07-15 22:45:16,652 - ConfigManager - INFO - Configuration saved successfully
2025-07-15 22:45:16,653 - ConfigManager - INFO - Configuration saved successfully
2025-07-15 22:45:16,654 - ConfigManager - INFO - Configuration saved successfully
2025-07-15 22:45:16,654 - ConfigManager - INFO - Proxy configuration updated from string
2025-07-15 22:45:16,654 - ConfigManager - INFO - Configuration saved successfully
2025-07-15 22:45:16,655 - ConfigManager - INFO - Configuration saved successfully
2025-07-15 22:45:16,656 - ConfigManager - INFO - Configuration saved successfully
2025-07-15 22:45:16,657 - ConfigManager - INFO - Configuration saved successfully
2025-07-15 22:45:16,657 - ConfigManager - INFO - Configuration saved successfully
2025-07-15 22:45:16,657 - ProxySetup - INFO - Initializing proxy manager...
2025-07-15 22:45:16,658 - ProxyManager - INFO - Proxy disabled in configuration
2025-07-15 22:45:16,658 - ProxySetup - INFO - Testing proxy connection...
2025-07-15 22:45:16,658 - ProxySetup - INFO - Testing proxy connection synchronously...
2025-07-15 22:45:16,661 - ProxySetup - INFO - The script will fall back to direct connection when proxy fails
2025-07-15 22:45:16,661 - ProxySetup - INFO - Creating legacy settings file...
2025-07-15 22:45:16,664 - ConfigManager - INFO - Configuration validation passed
2025-07-15 22:45:16,667 - ProxySetup - INFO - You can now run your groups.py script with enhanced proxy support
2025-07-15 22:45:37,381 - ProxySetup - INFO - Starting proxy setup...
2025-07-15 22:45:37,381 - ProxySetup - INFO - Setting up proxy configuration...
2025-07-15 22:45:37,381 - ConfigManager - INFO - Configuration loaded successfully
2025-07-15 22:45:37,381 - ConfigManager - INFO - Configuration saved successfully
2025-07-15 22:45:37,381 - ConfigManager - INFO - Configuration saved successfully
2025-07-15 22:45:37,381 - ConfigManager - INFO - Configuration saved successfully
2025-07-15 22:45:37,381 - ConfigManager - INFO - Configuration saved successfully
2025-07-15 22:45:37,397 - ConfigManager - INFO - Configuration saved successfully
2025-07-15 22:45:37,397 - ConfigManager - INFO - Proxy configuration updated from string
2025-07-15 22:45:37,398 - ConfigManager - INFO - Configuration saved successfully
2025-07-15 22:45:37,398 - ConfigManager - INFO - Configuration saved successfully
2025-07-15 22:45:37,398 - ConfigManager - INFO - Configuration saved successfully
2025-07-15 22:45:37,398 - ConfigManager - INFO - Configuration saved successfully
2025-07-15 22:45:37,398 - ConfigManager - INFO - Configuration saved successfully
2025-07-15 22:45:37,398 - ProxySetup - INFO - Initializing proxy manager...
2025-07-15 22:45:37,398 - ProxyManager - INFO - Proxy disabled in configuration
2025-07-15 22:45:37,398 - ProxySetup - INFO - Testing proxy connection...
2025-07-15 22:45:37,398 - ProxySetup - INFO - Testing proxy connection synchronously...
2025-07-15 22:45:37,405 - ProxySetup - INFO - The script will fall back to direct connection when proxy fails
2025-07-15 22:45:37,405 - ProxySetup - INFO - Creating legacy settings file...
2025-07-15 22:45:37,407 - ConfigManager - INFO - Configuration validation passed
2025-07-15 22:45:37,409 - ProxySetup - INFO - You can now run your groups.py script with enhanced proxy support
