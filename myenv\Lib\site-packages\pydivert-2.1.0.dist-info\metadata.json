{"classifiers": ["Development Status :: 4 - Beta", "Environment :: Win32 (MS Windows)", "Intended Audience :: <PERSON><PERSON><PERSON>", "Intended Audience :: System Administrators", "Intended Audience :: Telecommunications Industry", "License :: OSI Approved :: GNU Lesser General Public License v3 or later (LGPLv3+)", "Operating System :: Microsoft :: Windows :: Windows Vista", "Operating System :: Microsoft :: Windows :: Windows Server 2008", "Operating System :: Microsoft :: Windows :: Windows 7", "Programming Language :: Python", "Programming Language :: Python :: 2.7", "Programming Language :: Python :: 3.4", "Programming Language :: Python :: 3.5", "Programming Language :: Python :: 3.6", "Topic :: Software Development :: Libraries :: Python Modules", "Topic :: System :: Networking :: Firewalls", "Topic :: System :: Networking :: Monitoring", "Topic :: Utilities"], "download_url": "https://github.com/ffalcinelli/pydivert/releases/2.1.0", "extensions": {"python.details": {"contacts": [{"email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON>", "role": "author"}], "document_names": {"description": "DESCRIPTION.rst", "license": "LICENSE.txt"}, "project_urls": {"Home": "https://github.com/ffalcinelli/pydivert"}}}, "extras": ["docs", "test"], "generator": "bdist_wheel (0.30.0)", "keywords": ["windivert", "network", "tcp/ip"], "license": "LGPLv3", "metadata_version": "2.0", "name": "<PERSON><PERSON><PERSON><PERSON>", "run_requires": [{"extra": "test", "requires": ["codecov (>=2.0.5)", "hypothesis (>=3.5.3)", "mock (>=1.0.1)", "pytest (>=3.0.3)", "pytest-cov (>=2.2.1)", "pytest-faulthandler (<2,>=1.3.0)", "pytest-timeout (<2,>=1.0.0)", "wheel (>=0.29)"]}, {"extra": "docs", "requires": ["sphinx (>=1.4.8)"]}, {"environment": "python_version == \"2.7\" or python_version == \"3.3\"", "requires": ["enum34 (>=1.1.6)", "win-inet-pton (>=1.0.1)"]}], "summary": "Python binding to windivert driver", "version": "2.1.0"}