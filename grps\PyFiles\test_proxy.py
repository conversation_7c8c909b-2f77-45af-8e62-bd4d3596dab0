#!/usr/bin/env python3
"""
Test script for the enhanced proxy management system
"""

import asyncio
import logging
import sys
import os
import time
from proxy_manager import ProxyManager
from config_manager import ConfigManager, setup_proxycheap_config


def setup_logging():
    """Setup logging configuration"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler('proxy_test.log', encoding='utf-8'),
            logging.StreamHandler(sys.stdout)
        ]
    )
    return logging.getLogger("ProxyTest")


async def test_async_validation(proxy_manager):
    """Test asynchronous proxy validation"""
    logger = logging.getLogger("ProxyTest")
    logger.info("Testing async proxy validation...")
    
    start_time = time.time()
    is_valid, response_time, error_msg = await proxy_manager.validate_proxy_async()
    total_time = time.time() - start_time
    
    if is_valid:
        logger.info(f"[OK] Async validation successful!")
        logger.info(f"   Response time: {response_time:.2f}s")
        logger.info(f"   Total time: {total_time:.2f}s")
        return True
    else:
        logger.error(f"[FAIL] Async validation failed: {error_msg}")
        return False


def test_sync_validation(proxy_manager):
    """Test synchronous proxy validation"""
    logger = logging.getLogger("ProxyTest")
    logger.info("Testing sync proxy validation...")
    
    start_time = time.time()
    is_valid, response_time, error_msg = proxy_manager.validate_proxy_sync()
    total_time = time.time() - start_time
    
    if is_valid:
        logger.info(f"[OK] Sync validation successful!")
        logger.info(f"   Response time: {response_time:.2f}s")
        logger.info(f"   Total time: {total_time:.2f}s")
        return True
    else:
        logger.error(f"[FAIL] Sync validation failed: {error_msg}")
        return False


def test_health_monitoring(proxy_manager):
    """Test proxy health monitoring"""
    logger = logging.getLogger("ProxyTest")
    logger.info("Testing proxy health monitoring...")
    
    # Test multiple health checks
    results = []
    for i in range(3):
        logger.info(f"Health check {i+1}/3...")
        is_healthy = proxy_manager.check_health()
        results.append(is_healthy)
        
        stats = proxy_manager.get_proxy_stats()
        logger.info(f"   Health status: {'[HEALTHY]' if is_healthy else '[UNHEALTHY]'}")
        logger.info(f"   Success rate: {stats['success_rate']:.1%}")
        logger.info(f"   Total requests: {stats['total_requests']}")
        
        if i < 2:  # Don't sleep after last iteration
            time.sleep(2)
    
    success_rate = sum(results) / len(results)
    logger.info(f"Overall health check success rate: {success_rate:.1%}")
    
    return success_rate > 0.5


def test_fallback_behavior(proxy_manager):
    """Test fallback behavior"""
    logger = logging.getLogger("ProxyTest")
    logger.info("Testing fallback behavior...")
    
    # Force some failures to test fallback
    original_failure_count = proxy_manager.failure_count
    proxy_manager.failure_count = 10  # Simulate failures
    proxy_manager.total_requests = 15
    
    should_use = proxy_manager.should_use_proxy()
    logger.info(f"Should use proxy with high failure rate: {should_use}")
    
    # Reset to normal
    proxy_manager.failure_count = original_failure_count
    proxy_manager.total_requests = max(proxy_manager.total_requests - 15, 0)
    
    return True


def test_configuration(config_manager):
    """Test configuration management"""
    logger = logging.getLogger("ProxyTest")
    logger.info("Testing configuration management...")
    
    # Test basic configuration
    is_enabled = config_manager.is_proxy_enabled()
    proxy_url = config_manager.get_proxy_url()
    proxy_config = config_manager.get_proxy_config()
    
    logger.info(f"Proxy enabled: {is_enabled}")
    if proxy_url:
        # Mask password in URL for logging
        masked_url = proxy_url
        if '@' in proxy_url:
            parts = proxy_url.split('@')
            if len(parts) == 2:
                auth_part = parts[0].split('://')[-1]
                if ':' in auth_part:
                    username = auth_part.split(':')[0]
                    masked_url = proxy_url.replace(auth_part, f"{username}:***")
        logger.info(f"Proxy URL: {masked_url}")
    
    if proxy_config:
        logger.info(f"Proxy host: {proxy_config['host']}")
        logger.info(f"Proxy port: {proxy_config['port']}")
        logger.info(f"Connection timeout: {proxy_config['connection_timeout']}s")
    
    # Test configuration validation
    is_valid = config_manager.validate_config()
    logger.info(f"Configuration valid: {'[YES]' if is_valid else '[NO]'}")

    return is_valid


async def main():
    """Main test function"""
    logger = setup_logging()
    logger.info("[TEST] Starting proxy system tests...")
    
    # Setup configuration
    home = os.path.dirname(os.path.realpath(__file__)).replace("\\", "/")
    PROXY_STRING = "proxy-us.proxy-cheap.com:5959:pcDN3a0XvH-resfix-us-nnid-0:PC_7s8jdnEPolvdCiaOg"
    CONFIG_PATH = f"{home}/json/enhanced_settings.json"
    
    try:
        # Initialize configuration and proxy manager
        logger.info("Initializing configuration...")
        config_manager = setup_proxycheap_config(CONFIG_PATH, PROXY_STRING)
        
        logger.info("Initializing proxy manager...")
        proxy_manager = ProxyManager(CONFIG_PATH)
        
        # Run tests
        test_results = {}
        
        # Test 1: Configuration
        logger.info("\n" + "="*50)
        logger.info("TEST 1: Configuration Management")
        logger.info("="*50)
        test_results['configuration'] = test_configuration(config_manager)
        
        # Test 2: Sync validation
        logger.info("\n" + "="*50)
        logger.info("TEST 2: Synchronous Proxy Validation")
        logger.info("="*50)
        test_results['sync_validation'] = test_sync_validation(proxy_manager)
        
        # Test 3: Async validation
        logger.info("\n" + "="*50)
        logger.info("TEST 3: Asynchronous Proxy Validation")
        logger.info("="*50)
        test_results['async_validation'] = await test_async_validation(proxy_manager)
        
        # Test 4: Health monitoring
        logger.info("\n" + "="*50)
        logger.info("TEST 4: Health Monitoring")
        logger.info("="*50)
        test_results['health_monitoring'] = test_health_monitoring(proxy_manager)
        
        # Test 5: Fallback behavior
        logger.info("\n" + "="*50)
        logger.info("TEST 5: Fallback Behavior")
        logger.info("="*50)
        test_results['fallback_behavior'] = test_fallback_behavior(proxy_manager)
        
        # Summary
        logger.info("\n" + "="*50)
        logger.info("TEST SUMMARY")
        logger.info("="*50)
        
        passed_tests = sum(test_results.values())
        total_tests = len(test_results)
        
        for test_name, result in test_results.items():
            status = "[PASSED]" if result else "[FAILED]"
            logger.info(f"{test_name.replace('_', ' ').title()}: {status}")

        logger.info(f"\nOverall: {passed_tests}/{total_tests} tests passed")

        if passed_tests == total_tests:
            logger.info("[SUCCESS] All tests passed! Proxy system is working correctly.")
            return True
        else:
            logger.warning(f"[WARNING] {total_tests - passed_tests} tests failed. Check the logs for details.")
            return False
        
    except Exception as e:
        logger.error(f"❌ Test suite failed with error: {str(e)}")
        return False


if __name__ == "__main__":
    try:
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        success = loop.run_until_complete(main())
        loop.close()
        
        if success:
            print("\n[SUCCESS] Proxy system test completed successfully!")
            sys.exit(0)
        else:
            print("\n[FAILED] Proxy system test failed!")
            sys.exit(1)

    except KeyboardInterrupt:
        print("\n[INTERRUPTED] Test interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n[ERROR] Test failed with error: {str(e)}")
        sys.exit(1)
