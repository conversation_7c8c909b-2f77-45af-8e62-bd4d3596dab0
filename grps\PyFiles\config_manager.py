import json
import os
import logging
import time
from typing import Dict, Any, Optional


class ConfigManager:
    """
    Enhanced configuration manager for stealth settings and proxy management
    """
    
    def __init__(self, config_path: str):
        self.config_path = config_path
        self.logger = logging.getLogger("ConfigManager")
        self.config = self._load_config()
    
    def _load_config(self) -> Dict[str, Any]:
        """Load configuration from file or create default"""
        try:
            if os.path.exists(self.config_path):
                with open(self.config_path, 'r') as f:
                    config = json.load(f)
                    self.logger.info("Configuration loaded successfully")
                    return config
            else:
                self.logger.info("Configuration file not found, creating default")
                return self._create_default_config()
        except Exception as e:
            self.logger.error(f"Failed to load configuration: {str(e)}")
            return self._create_default_config()
    
    def _create_default_config(self) -> Dict[str, Any]:
        """Create default configuration"""
        default_config = {
            "proxy": {
                "enabled": False,
                "host": "",
                "port": 0,
                "username": "",
                "password": "",
                "protocol": "http",
                "health_check_interval": 300,
                "connection_timeout": 30,
                "read_timeout": 60,
                "max_retries": 3,
                "retry_delay": 5,
                "fallback_on_failure": True
            },
            "stealth": {
                "user_agent_rotation": True,
                "viewport_randomization": True,
                "request_delays": {
                    "min": 1.0,
                    "max": 3.0
                },
                "human_behavior": {
                    "mouse_movements": True,
                    "typing_delays": True,
                    "scroll_simulation": True
                }
            },
            "browser": {
                "headless": False,
                "disable_images": False,
                "disable_javascript": False,
                "window_size": {
                    "width": 1920,
                    "height": 1080
                }
            },
            "captcha": {
                "service": "2captcha",
                "api_key": "",
                "max_solve_time": 120,
                "auto_retry": True
            },
            "logging": {
                "level": "INFO",
                "file": "app.log",
                "max_size_mb": 10,
                "backup_count": 5
            }
        }
        
        self._save_config(default_config)
        return default_config
    
    def _save_config(self, config: Dict[str, Any] = None):
        """Save configuration to file"""
        try:
            config_to_save = config or self.config
            os.makedirs(os.path.dirname(self.config_path), exist_ok=True)
            
            with open(self.config_path, 'w') as f:
                json.dump(config_to_save, f, indent=4)
            self.logger.info("Configuration saved successfully")
        except Exception as e:
            self.logger.error(f"Failed to save configuration: {str(e)}")
    
    def get(self, key: str, default: Any = None) -> Any:
        """Get configuration value using dot notation (e.g., 'proxy.enabled')"""
        try:
            keys = key.split('.')
            value = self.config
            for k in keys:
                value = value[k]
            return value
        except (KeyError, TypeError):
            return default
    
    def set(self, key: str, value: Any):
        """Set configuration value using dot notation"""
        try:
            keys = key.split('.')
            config = self.config
            for k in keys[:-1]:
                if k not in config:
                    config[k] = {}
                config = config[k]
            config[keys[-1]] = value
            self._save_config()
        except Exception as e:
            self.logger.error(f"Failed to set configuration value: {str(e)}")
    
    def setup_proxy_from_string(self, proxy_string: str):
        """
        Setup proxy configuration from a proxy string
        Format: host:port:username:password
        """
        try:
            parts = proxy_string.split(':')
            if len(parts) >= 4:
                self.set('proxy.enabled', True)
                self.set('proxy.host', parts[0])
                self.set('proxy.port', int(parts[1]))
                self.set('proxy.username', parts[2])
                self.set('proxy.password', parts[3])
                self.logger.info("Proxy configuration updated from string")
                return True
            else:
                self.logger.error("Invalid proxy string format. Expected: host:port:username:password")
                return False
        except Exception as e:
            self.logger.error(f"Failed to setup proxy from string: {str(e)}")
            return False
    
    def get_proxy_config(self) -> Optional[Dict[str, Any]]:
        """Get complete proxy configuration"""
        if not self.get('proxy.enabled', False):
            return None
        
        return {
            'host': self.get('proxy.host'),
            'port': self.get('proxy.port'),
            'username': self.get('proxy.username'),
            'password': self.get('proxy.password'),
            'protocol': self.get('proxy.protocol', 'http'),
            'health_check_interval': self.get('proxy.health_check_interval', 300),
            'connection_timeout': self.get('proxy.connection_timeout', 30),
            'read_timeout': self.get('proxy.read_timeout', 60),
            'max_retries': self.get('proxy.max_retries', 3),
            'retry_delay': self.get('proxy.retry_delay', 5),
            'fallback_on_failure': self.get('proxy.fallback_on_failure', True)
        }
    
    def get_proxy_url(self) -> Optional[str]:
        """Get formatted proxy URL"""
        proxy_config = self.get_proxy_config()
        if not proxy_config:
            return None
        
        host = proxy_config['host']
        port = proxy_config['port']
        username = proxy_config['username']
        password = proxy_config['password']
        protocol = proxy_config['protocol']
        
        if username and password:
            return f"{protocol}://{username}:{password}@{host}:{port}"
        else:
            return f"{protocol}://{host}:{port}"
    
    def enable_proxy(self):
        """Enable proxy usage"""
        self.set('proxy.enabled', True)
        self.logger.info("Proxy enabled")
    
    def disable_proxy(self):
        """Disable proxy usage"""
        self.set('proxy.enabled', False)
        self.logger.info("Proxy disabled")
    
    def is_proxy_enabled(self) -> bool:
        """Check if proxy is enabled"""
        return self.get('proxy.enabled', False)
    
    def get_stealth_config(self) -> Dict[str, Any]:
        """Get stealth configuration"""
        return self.get('stealth', {})
    
    def get_browser_config(self) -> Dict[str, Any]:
        """Get browser configuration"""
        return self.get('browser', {})
    
    def get_captcha_config(self) -> Dict[str, Any]:
        """Get CAPTCHA configuration"""
        return self.get('captcha', {})
    
    def update_proxy_stats(self, stats: Dict[str, Any]):
        """Update proxy performance statistics"""
        try:
            if 'proxy_stats' not in self.config:
                self.config['proxy_stats'] = {}
            
            self.config['proxy_stats'].update(stats)
            self.config['proxy_stats']['last_updated'] = int(time.time())
            self._save_config()
        except Exception as e:
            self.logger.error(f"Failed to update proxy stats: {str(e)}")
    
    def get_proxy_stats(self) -> Dict[str, Any]:
        """Get proxy performance statistics"""
        return self.get('proxy_stats', {})
    
    def reset_config(self):
        """Reset configuration to defaults"""
        self.config = self._create_default_config()
        self.logger.info("Configuration reset to defaults")
    
    def validate_config(self) -> bool:
        """Validate current configuration"""
        try:
            # Validate proxy configuration
            if self.is_proxy_enabled():
                proxy_config = self.get_proxy_config()
                if not proxy_config or not proxy_config.get('host') or not proxy_config.get('port'):
                    self.logger.error("Invalid proxy configuration")
                    return False
            
            # Validate other critical settings
            if not isinstance(self.get('stealth.request_delays.min', 1.0), (int, float)):
                self.logger.error("Invalid request delay configuration")
                return False
            
            self.logger.info("Configuration validation passed")
            return True
            
        except Exception as e:
            self.logger.error(f"Configuration validation failed: {str(e)}")
            return False
    
    def export_config(self, export_path: str):
        """Export configuration to a different file"""
        try:
            with open(export_path, 'w') as f:
                json.dump(self.config, f, indent=4)
            self.logger.info(f"Configuration exported to {export_path}")
        except Exception as e:
            self.logger.error(f"Failed to export configuration: {str(e)}")
    
    def import_config(self, import_path: str):
        """Import configuration from a file"""
        try:
            with open(import_path, 'r') as f:
                imported_config = json.load(f)
            
            self.config.update(imported_config)
            self._save_config()
            self.logger.info(f"Configuration imported from {import_path}")
        except Exception as e:
            self.logger.error(f"Failed to import configuration: {str(e)}")


# Convenience function to setup proxy from ProxyCheap format
def setup_proxycheap_config(config_path: str, proxy_string: str) -> ConfigManager:
    """
    Setup configuration for ProxyCheap proxy
    Format: host:port:username:password
    """
    config_manager = ConfigManager(config_path)
    
    # Setup the specific ProxyCheap proxy
    if config_manager.setup_proxy_from_string(proxy_string):
        # Additional ProxyCheap specific settings
        config_manager.set('proxy.protocol', 'http')
        config_manager.set('proxy.health_check_interval', 180)  # 3 minutes for residential
        config_manager.set('proxy.connection_timeout', 45)     # Longer timeout for residential
        config_manager.set('proxy.max_retries', 5)             # More retries for residential
        config_manager.set('proxy.fallback_on_failure', True)
        
        return config_manager
    else:
        raise ValueError("Failed to setup ProxyCheap configuration")


if __name__ == "__main__":
    # Example usage
    import time
    home = os.path.dirname(os.path.realpath(__file__)).replace("\\", "/")
    CONFIG_PATH = f"{home}/json/settings.json"
    # Setup ProxyCheap proxy
    proxy_string = "proxy-us.proxy-cheap.com:5959:pcDN3a0XvH-resfix-us-nnid-0:PC_7s8jdnEPolvdCiaOg"
    config = setup_proxycheap_config(CONFIG_PATH, proxy_string)
    
    print("Proxy URL:", config.get_proxy_url())
    print("Proxy enabled:", config.is_proxy_enabled())
    print("Configuration valid:", config.validate_config())
