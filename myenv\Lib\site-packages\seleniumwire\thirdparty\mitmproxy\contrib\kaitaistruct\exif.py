# This is a generated file! Please edit source .ksy file and use kaitai-struct-compiler to rebuild

import array
import struct
import zlib
from enum import Enum

from kaitaistruct import BytesIO, KaitaiStream, KaitaiStruct
from kaitaistruct import __version__ as ks_version
from pkg_resources import parse_version

from .exif_be import ExifBe
from .exif_le import ExifLe

if parse_version(ks_version) < parse_version('0.7'):
    raise Exception("Incompatible Kaitai Struct Python API: 0.7 or later is required, but you have %s" % (ks_version))


class Exif(KaitaiStruct):
    def __init__(self, _io, _parent=None, _root=None):
        self._io = _io
        self._parent = _parent
        self._root = _root if _root else self
        self.endianness = self._io.read_u2le()
        _on = self.endianness
        if _on == 18761:
            self.body = ExifLe(self._io)
        elif _on == 19789:
            self.body = ExifBe(self._io)
