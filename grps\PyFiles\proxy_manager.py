import asyncio
import aiohttp
import json
import logging
import time
import random
from typing import Optional, Dict, Any, Tuple
from urllib.parse import urlparse
import requests


class ProxyManager:
    """
    Advanced single-proxy management system with health monitoring,
    authentication, and fallback capabilities.
    """
    
    def __init__(self, config_path: str = None):
        self.logger = logging.getLogger("ProxyManager")
        self.config_path = config_path
        self.proxy_config = None
        self.is_healthy = False
        self.last_health_check = 0
        self.health_check_interval = 300  # 5 minutes
        self.connection_timeout = 30
        self.read_timeout = 60
        self.max_retries = 3
        self.retry_delay = 5
        self.fallback_mode = False
        
        # Performance metrics
        self.success_count = 0
        self.failure_count = 0
        self.total_requests = 0
        self.average_response_time = 0
        self.last_successful_connection = 0
        
        self._load_config()
    
    def _load_config(self):
        """Load proxy configuration from settings file"""
        try:
            if self.config_path:
                with open(self.config_path, 'r') as f:
                    config = json.load(f)

                # Check for enhanced configuration format first
                if 'proxy' in config and isinstance(config['proxy'], dict):
                    proxy_config = config['proxy']
                    if proxy_config.get('enabled', False):
                        self.proxy_config = {
                            'host': proxy_config.get('host'),
                            'port': proxy_config.get('port'),
                            'username': proxy_config.get('username'),
                            'password': proxy_config.get('password'),
                            'protocol': proxy_config.get('protocol', 'http')
                        }
                        self.logger.info("Enhanced proxy configuration loaded successfully")
                    else:
                        self.logger.info("Enhanced proxy disabled in configuration")

                # Fallback to legacy configuration format
                elif config.get('use_proxy', False):
                    proxy_string = config.get('proxy', '')
                    if proxy_string:
                        self.proxy_config = self._parse_proxy_string(proxy_string)
                        self.logger.info("Legacy proxy configuration loaded successfully")
                    else:
                        self.logger.warning("Proxy enabled but no proxy string found")
                else:
                    self.logger.info("Proxy disabled in configuration")
        except Exception as e:
            self.logger.error(f"Failed to load proxy configuration: {str(e)}")
    
    def _parse_proxy_string(self, proxy_string: str) -> Dict[str, Any]:
        """
        Parse proxy string in format: host:port:username:password
        or *****************************:port
        """
        try:
            if '://' in proxy_string:
                # URL format: *****************************:port
                parsed = urlparse(proxy_string)
                return {
                    'host': parsed.hostname,
                    'port': parsed.port,
                    'username': parsed.username,
                    'password': parsed.password,
                    'protocol': parsed.scheme or 'http'
                }
            else:
                # String format: host:port:username:password
                parts = proxy_string.split(':')
                if len(parts) >= 4:
                    return {
                        'host': parts[0],
                        'port': int(parts[1]),
                        'username': parts[2],
                        'password': parts[3],
                        'protocol': 'http'
                    }
                elif len(parts) >= 2:
                    return {
                        'host': parts[0],
                        'port': int(parts[1]),
                        'username': None,
                        'password': None,
                        'protocol': 'http'
                    }
        except Exception as e:
            self.logger.error(f"Failed to parse proxy string: {str(e)}")
            return None
    
    def get_proxy_url(self) -> Optional[str]:
        """Get formatted proxy URL for use with requests/selenium"""
        if not self.proxy_config:
            return None
            
        config = self.proxy_config
        if config['username'] and config['password']:
            return f"{config['protocol']}://{config['username']}:{config['password']}@{config['host']}:{config['port']}"
        else:
            return f"{config['protocol']}://{config['host']}:{config['port']}"
    
    def get_selenium_proxy_args(self) -> list:
        """Get proxy arguments for Selenium Chrome options"""
        proxy_url = self.get_proxy_url()
        if not proxy_url:
            return []
        
        args = [f'--proxy-server={proxy_url}']
        
        # Add additional proxy-related Chrome arguments for better stealth
        args.extend([
            '--proxy-bypass-list=<-loopback>',  # Bypass proxy for localhost
            '--disable-proxy-certificate-handler',
            '--ignore-certificate-errors-spki-list',
            '--ignore-ssl-errors',
            '--allow-running-insecure-content'
        ])
        
        return args
    
    async def validate_proxy_async(self) -> Tuple[bool, float, str]:
        """
        Asynchronously validate proxy connection and measure response time
        Returns: (is_valid, response_time, error_message)
        """
        if not self.proxy_config:
            return False, 0, "No proxy configuration available"
        
        proxy_url = self.get_proxy_url()
        test_urls = [
            'https://httpbin.org/ip',
            'https://api.ipify.org?format=json',
            'https://ifconfig.me/ip'
        ]
        
        start_time = time.time()
        
        try:
            connector = aiohttp.TCPConnector(
                limit=10,
                limit_per_host=5,
                ttl_dns_cache=300,
                use_dns_cache=True,
            )
            
            timeout = aiohttp.ClientTimeout(
                total=self.connection_timeout,
                connect=15,
                sock_read=self.read_timeout
            )
            
            async with aiohttp.ClientSession(
                connector=connector,
                timeout=timeout
            ) as session:
                
                # Try multiple test URLs for reliability
                for test_url in test_urls:
                    try:
                        async with session.get(
                            test_url,
                            proxy=proxy_url,
                            headers={
                                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
                            }
                        ) as response:
                            if response.status == 200:
                                response_time = time.time() - start_time
                                content = await response.text()
                                self.logger.info(f"Proxy validation successful. Response time: {response_time:.2f}s")
                                return True, response_time, "Success"
                    except Exception as e:
                        self.logger.debug(f"Test URL {test_url} failed: {str(e)}")
                        continue
                
                return False, 0, "All test URLs failed"
                
        except Exception as e:
            response_time = time.time() - start_time
            error_msg = f"Proxy validation failed: {str(e)}"
            self.logger.error(error_msg)
            return False, response_time, error_msg
    
    def validate_proxy_sync(self) -> Tuple[bool, float, str]:
        """
        Synchronously validate proxy connection
        Returns: (is_valid, response_time, error_message)
        """
        if not self.proxy_config:
            return False, 0, "No proxy configuration available"
        
        proxy_url = self.get_proxy_url()
        proxies = {
            'http': proxy_url,
            'https': proxy_url
        }
        
        test_urls = [
            'https://httpbin.org/ip',
            'https://api.ipify.org?format=json'
        ]
        
        start_time = time.time()
        
        for test_url in test_urls:
            try:
                response = requests.get(
                    test_url,
                    proxies=proxies,
                    timeout=(15, 30),
                    headers={
                        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
                    }
                )
                
                if response.status_code == 200:
                    response_time = time.time() - start_time
                    self.logger.info(f"Proxy validation successful. Response time: {response_time:.2f}s")
                    return True, response_time, "Success"
                    
            except Exception as e:
                self.logger.debug(f"Test URL {test_url} failed: {str(e)}")
                continue
        
        response_time = time.time() - start_time
        error_msg = "All proxy validation attempts failed"
        self.logger.error(error_msg)
        return False, response_time, error_msg
    
    def check_health(self) -> bool:
        """
        Check proxy health and update status
        Returns: True if healthy, False otherwise
        """
        current_time = time.time()
        
        # Skip health check if recently performed
        if (current_time - self.last_health_check) < self.health_check_interval:
            return self.is_healthy
        
        self.logger.info("Performing proxy health check...")
        is_valid, response_time, error_msg = self.validate_proxy_sync()
        
        self.is_healthy = is_valid
        self.last_health_check = current_time
        
        if is_valid:
            self.success_count += 1
            self.last_successful_connection = current_time
            self.average_response_time = (
                (self.average_response_time * (self.success_count - 1) + response_time) / self.success_count
            )
        else:
            self.failure_count += 1
            
        self.total_requests += 1
        
        # Update fallback mode based on recent performance
        failure_rate = self.failure_count / max(self.total_requests, 1)
        if failure_rate > 0.5 and self.total_requests > 5:
            self.fallback_mode = True
            self.logger.warning("Enabling fallback mode due to high failure rate")
        elif failure_rate < 0.2 and self.fallback_mode:
            self.fallback_mode = False
            self.logger.info("Disabling fallback mode - proxy performance improved")
        
        return self.is_healthy
    
    def should_use_proxy(self) -> bool:
        """
        Determine if proxy should be used based on health and configuration
        """
        if not self.proxy_config:
            return False
            
        if self.fallback_mode:
            return False
            
        # Perform health check if needed
        return self.check_health()
    
    def get_proxy_stats(self) -> Dict[str, Any]:
        """Get proxy performance statistics"""
        return {
            'is_healthy': self.is_healthy,
            'fallback_mode': self.fallback_mode,
            'success_count': self.success_count,
            'failure_count': self.failure_count,
            'total_requests': self.total_requests,
            'success_rate': self.success_count / max(self.total_requests, 1),
            'average_response_time': self.average_response_time,
            'last_successful_connection': self.last_successful_connection,
            'last_health_check': self.last_health_check
        }
    
    def reset_stats(self):
        """Reset proxy performance statistics"""
        self.success_count = 0
        self.failure_count = 0
        self.total_requests = 0
        self.average_response_time = 0
        self.fallback_mode = False
        self.logger.info("Proxy statistics reset")
