#!/usr/bin/env python3
"""
Simple proxy setup script for ProxyCheap configuration
"""

import os
import json
import logging


def setup_logging():
    """Setup basic logging"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s'
    )
    return logging.getLogger("ProxySetup")


def create_enhanced_settings():
    """Create enhanced settings configuration"""
    logger = setup_logging()
    
    # ProxyCheap configuration
    proxy_config = {
        "proxy": {
            "enabled": True,
            "host": "proxy-us.proxy-cheap.com",
            "port": 5959,
            "username": "pcDN3a0XvH-resfix-us-nnid-0",
            "password": "PC_7s8jdnEPolvdCiaOg",
            "protocol": "http",
            "health_check_interval": 180,
            "connection_timeout": 45,
            "read_timeout": 60,
            "max_retries": 5,
            "retry_delay": 5,
            "fallback_on_failure": True
        },
        "stealth": {
            "user_agent_rotation": True,
            "viewport_randomization": True,
            "request_delays": {
                "min": 2.0,
                "max": 4.0
            },
            "human_behavior": {
                "mouse_movements": True,
                "typing_delays": True,
                "scroll_simulation": True
            }
        },
        "browser": {
            "headless": False,
            "disable_images": False,
            "disable_javascript": False,
            "window_size": {
                "width": 1920,
                "height": 1080
            }
        },
        "captcha": {
            "service": "2captcha",
            "api_key": "",
            "max_solve_time": 120,
            "auto_retry": True
        },
        "logging": {
            "level": "INFO",
            "file": "app.log",
            "max_size_mb": 10,
            "backup_count": 5
        }
    }
    
    # Create directory if it doesn't exist
    config_dir = "json"
    os.makedirs(config_dir, exist_ok=True)
    
    # Save enhanced settings
    enhanced_path = os.path.join(config_dir, "enhanced_settings.json")
    with open(enhanced_path, 'w') as f:
        json.dump(proxy_config, f, indent=4)
    
    logger.info(f"Enhanced settings created at: {enhanced_path}")
    return enhanced_path


def create_legacy_settings():
    """Create legacy settings for backward compatibility"""
    logger = setup_logging()
    
    legacy_config = {
        "use_proxy": True,
        "proxy": "http://pcDN3a0XvH-resfix-us-nnid-0:<EMAIL>:5959"
    }
    
    # Create directory if it doesn't exist
    config_dir = "json"
    os.makedirs(config_dir, exist_ok=True)
    
    # Save legacy settings
    legacy_path = os.path.join(config_dir, "settings.json")
    with open(legacy_path, 'w') as f:
        json.dump(legacy_config, f, indent=4)
    
    logger.info(f"Legacy settings created at: {legacy_path}")
    return legacy_path


def test_proxy_simple():
    """Simple proxy test using requests"""
    logger = setup_logging()
    
    try:
        import requests
        
        proxy_url = "http://pcDN3a0XvH-resfix-us-nnid-0:<EMAIL>:5959"
        proxies = {
            'http': proxy_url,
            'https': proxy_url
        }
        
        logger.info("Testing proxy connection...")
        response = requests.get(
            'https://httpbin.org/ip',
            proxies=proxies,
            timeout=30,
            headers={'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'}
        )
        
        if response.status_code == 200:
            ip_info = response.json()
            logger.info(f"[SUCCESS] Proxy test successful!")
            logger.info(f"Your IP through proxy: {ip_info.get('origin', 'Unknown')}")
            return True
        else:
            logger.error(f"[FAILED] Proxy test failed with status: {response.status_code}")
            return False
            
    except Exception as e:
        logger.error(f"[ERROR] Proxy test failed: {str(e)}")
        return False


def main():
    """Main setup function"""
    logger = setup_logging()
    
    print("=" * 60)
    print("SIMPLE PROXY SETUP FOR PROXYCHEAP")
    print("=" * 60)
    
    try:
        # Create configuration files
        logger.info("Creating configuration files...")
        enhanced_path = create_enhanced_settings()
        legacy_path = create_legacy_settings()
        
        # Test proxy
        logger.info("Testing proxy connection...")
        if test_proxy_simple():
            logger.info("[SUCCESS] Proxy setup completed successfully!")
            
            print("\n" + "=" * 60)
            print("SETUP COMPLETE")
            print("=" * 60)
            print(f"Enhanced settings: {enhanced_path}")
            print(f"Legacy settings: {legacy_path}")
            print("Proxy: proxy-us.proxy-cheap.com:5959")
            print("Status: ENABLED and TESTED")
            print("\nYou can now run:")
            print("  python test_proxy.py")
            print("  python groups.py")
            print("=" * 60)
            
            return True
        else:
            logger.warning("[WARNING] Proxy setup completed but test failed")
            logger.info("Configuration files created, but proxy may not be working")
            logger.info("The system will automatically fallback to direct connection")
            return False
            
    except Exception as e:
        logger.error(f"[ERROR] Setup failed: {str(e)}")
        return False


def disable_proxy():
    """Disable proxy in both configuration files"""
    logger = setup_logging()
    
    try:
        # Disable in enhanced settings
        enhanced_path = "json/enhanced_settings.json"
        if os.path.exists(enhanced_path):
            with open(enhanced_path, 'r') as f:
                config = json.load(f)
            config['proxy']['enabled'] = False
            with open(enhanced_path, 'w') as f:
                json.dump(config, f, indent=4)
            logger.info("Proxy disabled in enhanced settings")
        
        # Disable in legacy settings
        legacy_path = "json/settings.json"
        if os.path.exists(legacy_path):
            with open(legacy_path, 'r') as f:
                config = json.load(f)
            config['use_proxy'] = False
            with open(legacy_path, 'w') as f:
                json.dump(config, f, indent=4)
            logger.info("Proxy disabled in legacy settings")
        
        logger.info("[SUCCESS] Proxy disabled successfully")
        return True
        
    except Exception as e:
        logger.error(f"[ERROR] Failed to disable proxy: {str(e)}")
        return False


def enable_proxy():
    """Enable proxy in both configuration files"""
    logger = setup_logging()
    
    try:
        # Enable in enhanced settings
        enhanced_path = "json/enhanced_settings.json"
        if os.path.exists(enhanced_path):
            with open(enhanced_path, 'r') as f:
                config = json.load(f)
            config['proxy']['enabled'] = True
            with open(enhanced_path, 'w') as f:
                json.dump(config, f, indent=4)
            logger.info("Proxy enabled in enhanced settings")
        
        # Enable in legacy settings
        legacy_path = "json/settings.json"
        if os.path.exists(legacy_path):
            with open(legacy_path, 'r') as f:
                config = json.load(f)
            config['use_proxy'] = True
            with open(legacy_path, 'w') as f:
                json.dump(config, f, indent=4)
            logger.info("Proxy enabled in legacy settings")
        
        # Test proxy
        if test_proxy_simple():
            logger.info("[SUCCESS] Proxy enabled and tested successfully")
        else:
            logger.warning("[WARNING] Proxy enabled but test failed")
        
        return True
        
    except Exception as e:
        logger.error(f"[ERROR] Failed to enable proxy: {str(e)}")
        return False


if __name__ == "__main__":
    import sys
    
    if len(sys.argv) > 1:
        command = sys.argv[1].lower()
        
        if command == "setup":
            main()
        elif command == "enable":
            enable_proxy()
        elif command == "disable":
            disable_proxy()
        elif command == "test":
            test_proxy_simple()
        else:
            print("Usage: python setup_proxy_simple.py [setup|enable|disable|test]")
            print("  setup   - Setup proxy configuration and test")
            print("  enable  - Enable proxy usage")
            print("  disable - Disable proxy usage")
            print("  test    - Test proxy connection")
    else:
        # Default action is setup
        main()
