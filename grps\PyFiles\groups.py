from selenium.webdriver.chrome.service import Service as ChromeService
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import NoSuchElementException
from webdriver_manager.chrome import ChromeDriverManager
from selenium.webdriver.support.ui import WebDriverWait
from selenium.common.exceptions import TimeoutException
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.common.by import By
from twocaptcha import TwoCaptch<PERSON>
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
import os, random, logging, json, psutil, secrets, requests
import subprocess, shutil, msvcrt, time, sys, re
from random import choice, randint, uniform
from unidecode import unidecode
from string import digits
from time import sleep

# Import enhanced proxy management
try:
    from proxy_manager import ProxyManager
    from config_manager import ConfigManager
    ENHANCED_PROXY_AVAILABLE = True
except ImportError:
    ENHANCED_PROXY_AVAILABLE = False
    print("Enhanced proxy management not available. Using legacy proxy system.")



os.environ['TF_CPP_MIN_LOG_LEVEL'] = '3'
home = os.path.dirname(os.path.realpath(__file__)).replace("\\", "/")
Files_home = home.replace('PyFiles','')
profile_home = f"{home.replace('PyFiles','')}Profiles"
data_file = f"{home.replace('PyFiles','')}data.txt"
gmail_account_file = f"{home.replace('PyFiles','')}Gmail_Accounts"
data_directory = f"{home}/Data"
gmail_map_file = f"{home}/json/GmailAccountsMap.json"
map_path = f"{home}/json/GroupsMap.json"
dead_accounts = f"{home}/DeadAccounts.txt"
ua_map = f"{home}/json/ua-lib.json"
proxy_file = f"{home}/proxy.txt"
settings_path = f"{home}/json/settings.json"
cp_xpaths = [
    "//div[@id='rc-anchor-container']",
    "//div[@id='recaptcha-accessible-status']",
    "//span[contains(@class, 'recaptcha-checkbox')]"
]



class CityName():
    def __init__(self) -> None:
        self.country_codes = ["US","AU","GB","FR","DE","IS","NO"]
        self.cd = secrets.choice(self.country_codes)
        
    def v2(self):
        url = "https://wft-geo-db.p.rapidapi.com/v1/geo/cities"
        querystring = {"limit":"10",
                        #"minPopulation": f'{pop}',
                        "countryIds": f'{self.cd}'}
        headers = {
            'x-rapidapi-host': "wft-geo-db.p.rapidapi.com",
            'x-rapidapi-key': "**************************************************"
            }
        resp = requests.request("GET", url, headers=headers, params=querystring).text
        info = json.loads(resp)
        return info
    
    def v1(self):
            url = 'https://countriesnow.space/api/v0.1/countries/population/cities'
            resp = requests.get(url).text
            info = json.loads(resp)
            return info
        
    
    
    def name(self):
        accents = ['à' ,'â', 'ä','é','è','ê' ,'ë' ,'ú' ,'ï'  ,'î' ,'ô' , 'ó'  ,'ö'  ,'ù'  ,'û' ,'ü' ,'ÿ' ,'ç','í','á', 'ạ' ,'à', 'ả', 'ã'
                'ă', 'ắ' ,'ặ', 'ằ' ,'ẳ','ẵ',
                'â', 'ấ' ,'ậ' ,'ầ', 'ẩ ','ẫ',
                'é', 'ẹ', 'è', 'ẻ', 'ẽ',
                'ê' ,'ế' ,'ệ', 'ề' ,'ể', 'ễ'
                'i' ,'í' ,'ị' ,'ì' ,'ỉ', 'ĩ']
        try:
            self.data = self.v1()
            lim = randint(1, 500)
            city = self.data['data'][lim]['city'].lower()
        except:
            self.data = self.v2()
            indx = randint(0, 9)
            city = self.data["data"][indx]["city"].lower()
        city = city.replace(" ","").replace("@","").replace(",","").replace(";","").replace("'","").replace("(","").replace(")","")
        accents_contains = any(accent in city for accent in accents)
        if accents_contains:
            city = unidecode(city)
        city = city.capitalize()
        return city


class Driver():
    def __init__(self,email,password,ua_agent,index):
        global ENHANCED_PROXY_AVAILABLE

        logging.basicConfig(
            filename='app.log',
            level=logging.DEBUG,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        )
        self.logger = logging.getLogger("Driver")
        self.failed = False
        self.email = email
        self.password = password
        self.conf = None
        self.index = index

        # Initialize enhanced proxy management if available
        self.proxy_manager = None
        self.config_manager = None
        self.enhanced_proxy_available = ENHANCED_PROXY_AVAILABLE  # Store local copy

        if self.enhanced_proxy_available:
            try:
                enhanced_settings_path = f"{home}/json/enhanced_settings.json"
                self.config_manager = ConfigManager(enhanced_settings_path)
                self.proxy_manager = ProxyManager(enhanced_settings_path)
                self.logger.info("Enhanced proxy management initialized")
            except Exception as e:
                self.logger.warning(f"Failed to initialize enhanced proxy management: {str(e)}")
                self.enhanced_proxy_available = False

        # Create browser with enhanced proxy support
        self.browser = self._create_browser_with_proxy(ua_agent, index)

    def _create_browser_with_proxy(self, ua_agent, index):
        """Create browser with enhanced proxy support and stealth features"""
        options = Options()

        # Basic configuration
        options.add_argument(f"user-agent={ua_agent}")
        options.add_argument("--lang=fr_FR")
        options.add_argument("--start-maximized")
        options.add_argument('--disable-popup-blocking')
        options.add_argument('--disable-blink-features=AutomationControlled')
        options.add_experimental_option("excludeSwitches", ['enable-automation'])
        options.add_experimental_option('useAutomationExtension', False)
        options.add_argument('--disable-infobars')
        options.add_argument(f"--user-data-dir={profile_home}/{self.email}")
        options.add_argument('--disable-logging')
        options.add_argument('--log-level=3')
        options.add_argument('--silent')

        # Enhanced stealth options
        options.add_argument('--disable-dev-shm-usage')
        options.add_argument('--no-sandbox')
        options.add_argument('--disable-webrtc')  # WebRTC leak protection
        options.add_argument('--disable-webrtc-multiple-routes')
        options.add_argument('--disable-features=VizDisplayCompositor')

        # SSL/Security options for stealth proxy
        options.add_argument('--ignore-certificate-errors')
        options.add_argument('--ignore-ssl-errors')
        options.add_argument('--ignore-certificate-errors-spki-list')
        options.add_argument('--ignore-urlfetcher-cert-requests')
        options.add_argument('--disable-web-security')
        options.add_argument('--allow-running-insecure-content')
        options.add_argument('--disable-extensions-http-throttling')
        options.add_argument('--disable-features=VizDisplayCompositor')
        options.add_argument('--disable-component-extensions-with-background-pages')
        options.add_argument('--disable-default-apps')
        options.add_argument('--disable-extensions')
        options.add_argument('--disable-background-timer-throttling')
        options.add_argument('--disable-renderer-backgrounding')
        options.add_argument('--disable-backgrounding-occluded-windows')
        options.add_argument('--disable-client-side-phishing-detection')
        options.add_argument('--disable-sync')
        options.add_argument('--metrics-recording-only')
        options.add_argument('--no-report-upload')
        options.add_argument('--disable-background-networking')
        options.add_argument('--disable-domain-reliability')
        options.add_argument('--disable-ipc-flooding-protection')

        # Random debugging port
        port = random.randint(9888, 11000)
        options.add_argument(f'--remote-debugging-port={port + index}')

        # Proxy configuration with enhanced management
        proxy_configured = False
        if self.enhanced_proxy_available and self.proxy_manager:
            if self.proxy_manager.should_use_proxy():
                proxy_url = self.proxy_manager.get_proxy_url()
                self.logger.info(f"Using enhanced proxy: {proxy_url}")

                # Try selenium-wire first for better proxy authentication
                try:
                    from seleniumwire import webdriver as wire_webdriver
                    self.logger.info("selenium-wire imported successfully for enhanced proxy")

                    # Configure selenium-wire proxy options with better error handling
                    seleniumwire_options = {
                        'proxy': {
                            'http': proxy_url,
                            'https': proxy_url,
                            'no_proxy': 'localhost,127.0.0.1,*.local'
                        },
                        'verify_ssl': False,
                        'suppress_connection_errors': True,
                        'connection_timeout': 60,
                        'read_timeout': 120,
                        'request_storage_base_dir': None,
                        'auto_config': False
                    }

                    self.logger.info(f"Configuring selenium-wire with enhanced proxy: {proxy_url.split('@')[0]}@***")

                    # Create Chrome service
                    chrome_service = ChromeService(executable_path=ChromeDriverManager().install())
                    chrome_service.creationflags = subprocess.CREATE_NO_WINDOW

                    # Create browser with selenium-wire and error handling
                    try:
                        browser = wire_webdriver.Chrome(
                            service=chrome_service,
                            options=options,
                            seleniumwire_options=seleniumwire_options
                        )
                        browser.set_page_load_timeout(120)

                        # Test proxy connection immediately
                        test_js = """
                        return new Promise((resolve) => {
                            fetch('https://httpbin.org/ip', {method: 'GET'})
                                .then(response => response.json())
                                .then(data => resolve(data.origin))
                                .catch(error => resolve('proxy_test_failed'));
                        });
                        """

                        try:
                            proxy_test_result = browser.execute_async_script(test_js)
                            if proxy_test_result and proxy_test_result != 'proxy_test_failed':
                                self.logger.info(f"Proxy test successful, IP: {proxy_test_result}")
                            else:
                                self.logger.warning("Proxy test failed, but continuing...")
                        except:
                            self.logger.warning("Could not test proxy, but continuing...")

                    except Exception as proxy_error:
                        self.logger.error(f"selenium-wire proxy creation failed: {str(proxy_error)}")
                        # Try without seleniumwire_options as fallback
                        browser = wire_webdriver.Chrome(
                            service=chrome_service,
                            options=options
                        )
                        browser.set_page_load_timeout(120)

                    # Execute comprehensive stealth JavaScript
                    stealth_js = """
                    // Remove webdriver property
                    Object.defineProperty(navigator, 'webdriver', {
                        get: () => undefined,
                    });

                    // Hide automation indicators
                    Object.defineProperty(navigator, 'plugins', {
                        get: () => [1, 2, 3, 4, 5],
                    });

                    // Override chrome runtime
                    window.chrome = {
                        runtime: {}
                    };

                    // Hide selenium indicators
                    Object.defineProperty(navigator, 'languages', {
                        get: () => ['en-US', 'en'],
                    });

                    // Remove automation flags
                    delete window.cdc_adoQpoasnfa76pfcZLmcfl_Array;
                    delete window.cdc_adoQpoasnfa76pfcZLmcfl_Promise;
                    delete window.cdc_adoQpoasnfa76pfcZLmcfl_Symbol;
                    """
                    browser.execute_script(stealth_js)

                    # Hide security warnings with CSS injection
                    hide_warnings_css = """
                    var style = document.createElement('style');
                    style.innerHTML = `
                        /* Hide security warnings */
                        .security-state-insecure,
                        .security-state-warning,
                        .security-state-dangerous,
                        [data-test-id="security-state-chip"],
                        .omnibox-security-chip,
                        .location-bar-security-chip,
                        .security-chip,
                        .page-info-security-content,
                        .security-state-chip {
                            display: none !important;
                            visibility: hidden !important;
                        }

                        /* Hide "Not secure" text */
                        [aria-label*="Not secure"],
                        [aria-label*="Non sécurisé"],
                        [title*="Not secure"],
                        [title*="Non sécurisé"] {
                            display: none !important;
                        }
                    `;
                    document.head.appendChild(style);
                    """

                    try:
                        browser.execute_script(hide_warnings_css)
                    except:
                        pass  # Ignore if CSS injection fails

                    self.logger.info(f"Browser created successfully with selenium-wire enhanced proxy for {self.email}")
                    sleep(5)
                    return browser

                except ImportError:
                    self.logger.warning("selenium-wire not available, using standard enhanced proxy method")
                    proxy_args = self.proxy_manager.get_selenium_proxy_args()
                    for arg in proxy_args:
                        options.add_argument(arg)
                    proxy_configured = True
                except Exception as e:
                    self.logger.error(f"selenium-wire enhanced proxy failed: {str(e)}, trying alternative approach")

                    # Try with minimal selenium-wire options
                    try:
                        minimal_options = {
                            'proxy': {
                                'http': proxy_url,
                                'https': proxy_url
                            },
                            'verify_ssl': False,
                            'suppress_connection_errors': True
                        }

                        chrome_service = ChromeService(executable_path=ChromeDriverManager().install())
                        chrome_service.creationflags = subprocess.CREATE_NO_WINDOW

                        browser = wire_webdriver.Chrome(
                            service=chrome_service,
                            options=options,
                            seleniumwire_options=minimal_options
                        )
                        browser.set_page_load_timeout(120)

                        self.logger.info(f"Browser created with minimal selenium-wire proxy for {self.email}")
                        sleep(5)
                        return browser

                    except Exception as e2:
                        self.logger.error(f"Minimal selenium-wire also failed: {str(e2)}, falling back to standard method")
                        proxy_args = self.proxy_manager.get_selenium_proxy_args()
                        for arg in proxy_args:
                            options.add_argument(arg)
                        proxy_configured = True
            else:
                self.logger.info("Enhanced proxy available but not healthy, using direct connection")

        # Fallback to legacy proxy system with selenium-wire
        if not proxy_configured:
            try:
                with open(settings_path, 'r') as settings_file:
                    settings = json.load(settings_file)
                if settings.get('use_proxy', False):
                    proxy = settings.get('proxy')
                    if proxy:
                        self.logger.info(f"Attempting to use legacy proxy with selenium-wire: {proxy}")

                        # Try to use selenium-wire for better proxy authentication support
                        try:
                            from seleniumwire import webdriver as wire_webdriver
                            self.logger.info("selenium-wire imported successfully")

                            # Configure selenium-wire proxy options with SSL handling
                            seleniumwire_options = {
                                'proxy': {
                                    'http': proxy,
                                    'https': proxy,
                                    'no_proxy': 'localhost,127.0.0.1'
                                },
                                'verify_ssl': False,  # Disable SSL verification for proxy
                                'suppress_connection_errors': True,
                                'connection_timeout': 30,
                                'read_timeout': 60
                            }

                            self.logger.info(f"Configuring selenium-wire with proxy: {proxy.split('@')[0]}@***")

                            # Create Chrome service
                            chrome_service = ChromeService(executable_path=ChromeDriverManager().install())
                            chrome_service.creationflags = subprocess.CREATE_NO_WINDOW

                            # Create browser with selenium-wire
                            browser = wire_webdriver.Chrome(
                                service=chrome_service,
                                options=options,
                                seleniumwire_options=seleniumwire_options
                            )
                            browser.set_page_load_timeout(120)

                            # Execute stealth JavaScript
                            stealth_js = """
                            Object.defineProperty(navigator, 'webdriver', {
                                get: () => undefined,
                            });
                            """
                            browser.execute_script(stealth_js)

                            self.logger.info(f"Browser created successfully with selenium-wire proxy for {self.email}")
                            sleep(5)
                            return browser

                        except ImportError:
                            self.logger.warning("selenium-wire not available, falling back to standard proxy method")
                            # Fall back to standard Chrome proxy (may not work with auth)
                            options.add_argument(f'--proxy-server={proxy}')
                            self.logger.info(f"Using legacy proxy (may have auth issues): {proxy}")
                            proxy_configured = True
                        except Exception as e:
                            self.logger.error(f"selenium-wire failed: {str(e)}, falling back to standard proxy")
                            # Fall back to standard Chrome proxy
                            options.add_argument(f'--proxy-server={proxy}')
                            self.logger.info(f"Using fallback proxy method: {proxy}")
                            proxy_configured = True

            except Exception as e:
                self.logger.warning(f"Failed to load legacy proxy settings: {str(e)}")

        if not proxy_configured:
            self.logger.info("No proxy configured, using direct connection")

        # Create Chrome service
        chrome_service = ChromeService(executable_path=ChromeDriverManager().install())
        chrome_service.creationflags = subprocess.CREATE_NO_WINDOW

        try:
            browser = webdriver.Chrome(service=chrome_service, options=options)
            browser.set_page_load_timeout(120)

            # Execute stealth JavaScript
            stealth_js = """
            Object.defineProperty(navigator, 'webdriver', {
                get: () => undefined,
            });
            """
            browser.execute_script(stealth_js)

            self.logger.info(f"Browser created successfully for {self.email}")
            sleep(5)
            return browser

        except Exception as e:
            self.logger.error(f"Failed to create browser: {str(e)}")
            # If enhanced proxy fails, try fallback
            if self.enhanced_proxy_available and self.proxy_manager and proxy_configured:
                self.logger.info("Retrying browser creation with proxy fallback...")
                self.proxy_manager.fallback_mode = True
                return self._create_browser_with_proxy(ua_agent, index)
            raise e


    def go(self,url):
        """Navigate to URL with enhanced error handling and retry logic"""
        self.url = url
        max_retries = 3
        retry_delay = 2

        for attempt in range(max_retries):
            try:
                self.logger.info(f"Navigating to {url} (attempt {attempt + 1})")
                self.browser.get(url)

                # Add random delay to simulate human behavior
                delay = random.uniform(2.0, 4.0)
                sleep(delay)
                return True

            except Exception as e:
                error_msg = str(e)
                self.logger.error(f"Navigation attempt {attempt + 1} failed: {error_msg}")

                # Check for proxy-related errors
                if any(err in error_msg for err in [
                    "ERR_PROXY_CONNECTION_FAILED",
                    "ERR_CONNECTION_RESET",
                    "ERR_TUNNEL_CONNECTION_FAILED",
                    "net::"
                ]):
                    self.logger.warning("Proxy error detected")

                    # If enhanced proxy is available, enable fallback mode
                    if self.enhanced_proxy_available and self.proxy_manager:
                        self.logger.warning("Enabling proxy fallback mode")
                        self.proxy_manager.fallback_mode = True

                        # If this is not the last attempt, recreate browser without proxy
                        if attempt < max_retries - 1:
                            self.logger.info("Recreating browser without proxy...")
                            try:
                                self.browser.quit()
                            except:
                                pass
                            self.browser = self._create_browser_with_proxy(self.browser.execute_script("return navigator.userAgent;"), self.index)
                            continue

                if attempt < max_retries - 1:
                    sleep(retry_delay * (attempt + 1))  # Exponential backoff
                else:
                    raise e

        return False


    def get_proxy_stats(self):
        """Get current proxy performance statistics"""
        if self.enhanced_proxy_available and self.proxy_manager:
            return self.proxy_manager.get_proxy_stats()
        return {"enhanced_proxy": False, "message": "Enhanced proxy not available"}

    def finish(self):
        try:
            # Log proxy statistics if available
            if self.enhanced_proxy_available and self.proxy_manager:
                stats = self.get_proxy_stats()
                self.logger.info(f"Session proxy stats: {stats}")

            self.browser.close()
        except:
            try:
                self.browser.quit()
            except Exception as e:
                self.logger.error(f"{str(e)}")


    def f5(self):
        self.browser.refresh()
    

    def find_class(self, class_name):
        elements = self.browser.find_element(By.CLASS_NAME, class_name)
        sleep(1)
        return elements
    

    def find_xpath(self,xpath):
        elements = self.browser.find_element(By.XPATH,xpath)
        sleep(1)
        return elements


    def find_xpath_all(self,xpath):
        elements = self.browser.find_elements(By.XPATH,xpath)
        sleep(1)
        return elements


    def find_css(self,CSS):
        element = self.browser.find_element(By.CSS_SELECTOR,CSS)
        sleep(1)
        return element

    def find_css_all(self,CSS):
        element = self.browser.find_elements(By.CSS_SELECTOR,CSS)
        return element

    
    def execute_js(self,js):
        self.res = self.browser.execute_script(js)
        sleep(0.5)
        return self.res


    def running(self):
        try:
            title = self.browser.title
            if title:
                return True
        except:
            try:
                self.browser.current_url
                return True
            except:
                return False


    def this_url(self):
        return self.browser.current_url
    

    def title(self):
        return self.browser.title


    def scrol_down(self,limit=None):
        if limit is None:
            limit = 300
        html_tag = self.browser.find_element(By.TAG_NAME,"html")
        i=0
        match = True
        while match:
            html_tag.send_keys(Keys.DOWN)
            i+=1
            if i == limit:
                match = False


    def get_callback(self):
        try:
            callback = self.execute_js("""
                function findRecaptchaClients() {
                            if (typeof (___grecaptcha_cfg) !== 'undefined') {
                                return Object.entries(___grecaptcha_cfg.clients).map(([cid, client]) => {
                                const data = { id: cid, version: cid >= 10000 ? 'V3' : 'V2' };
                                const objects = Object.entries(client).filter(([_, value]) => value && typeof value === 'object');

                                objects.forEach(([toplevelKey, toplevel]) => {
                                    const found = Object.entries(toplevel).find(([_, value]) => (
                                    value && typeof value === 'object' && 'sitekey' in value && 'size' in value
                                    ));
                                
                                    if (typeof toplevel === 'object' && toplevel instanceof HTMLElement && toplevel['tagName'] === 'DIV'){
                                        data.pageurl = toplevel.baseURI;
                                    }
                                    
                                    if (found) {
                                    const [sublevelKey, sublevel] = found;

                                    data.sitekey = sublevel.sitekey;
                                    const callbackKey = data.version === 'V2' ? 'callback' : 'promise-callback';
                                    const callback = sublevel[callbackKey];
                                    if (!callback) {
                                        data.callback = null;
                                        data.function = null;
                                    } else {
                                        data.function = callback;
                                        const keys = [cid, toplevelKey, sublevelKey, callbackKey].map((key) => `['${key}']`).join('');
                                        data.callback = `___grecaptcha_cfg.clients${keys}`;
                                    }
                                    }
                                });
                                return data;
                                });
                            }
                            return [];
                            }
                            const callback = findRecaptchaClients();
                            let callToken = null;
                            if (callback.length > 0) {
                                callToken = callback[0]["callback"];
                            }
                            return callToken;         
                            """)
            return callback
        except Exception as e:
            self.logger.error(f"{str(e)}")
            return None


    def wait_xpath_frame(self,xpath,timeout=None):
        if timeout is None:
            timeout = 25
        self.elem = WebDriverWait(self.browser,timeout).until(EC.frame_to_be_available_and_switch_to_it((By.XPATH,xpath)))
        sleep(1)
        return self.elem
    

    def wait_css_clickable(self, css, timeout=None):
        if timeout is None:
            timeout = 25
        return WebDriverWait(self.browser, timeout).until(EC.element_to_be_clickable((By.CSS_SELECTOR, css)))


    def wait_xpath_presence(self, xpath, timeout=None):
        if timeout is None:
            timeout = 120

        return   WebDriverWait(self.browser, timeout).until(EC.presence_of_element_located((By.XPATH, xpath)))
        
    

    def switch_back(self):
        self.browser.witch_to.default_content()


    def get_cookies(self):
        try:
            return self.browser.get_cookies()
        except Exception as e:
            self.logger.error(f"Error getting cookies: {str(e)}")
            return None
        


    def add_cookie(self,cookie):
        try:
            self.browser.add_cookie(cookie)
        except Exception as e:
            self.logger.error(f"Error adding cookies: {str(e)}")



class Worker():
    def __init__(self,actions):
        super().__init__()
        self.actions = actions
        logging.basicConfig(
            filename='app.log',
            level=logging.DEBUG,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        )
        self.logger = logging.getLogger("Worker")


    def terminate_selenium_driver(self):
        try:
            for process in psutil.process_iter(attrs=['pid', 'name', 'cmdline']):
                process_info = process.info
                if 'chromedriver' in process_info.get('name'):
                    process.terminate()
        except Exception as e:
            self.logger.error(f"{str(e)}")


    def remove_profile(self, email):
        try:
            profile = f"{profile_home}/{email}"
            if os.path.exists(profile):
                # Use shutil.rmtree to delete the directory and its contents
                shutil.rmtree(profile)
                self.logger.info(f"Profile {profile} removed successfully.")
            else:
                self.logger.warning(f"Profile {profile} does not exist.")
        except Exception as e:
            self.logger.error(f"Error removing profile {profile}: {str(e)}")


    def login_Wait(self):
        self.logger.warning("=== Infinite Wait Is Enable to Stop, please close browser ===")
        while self.browser.running() == True:
            sleep(0.5)

    def wait(self):
        while self.browser.running() == True:
            sleep(2.5)


    def wait_for_verification(self):
        while self.browser.running() == True and "signin/challenge/iap" in self.browser.this_url():
            sleep(2.5)



    def add_group(self, email, group):
        data = {}
        if os.path.exists(map_path):
            with open(map_path, 'r') as f:
                data = json.load(f)

        if email in data:
            for g in data[email]:
                if g["name"] == group:
                    g["members_num"] = 0
                    break
            else:
                data[email].append({
                    "name": group, 
                    "members_num": 0
                })
        else:
            data[email] = [{
                "name": group, 
                "members_num": 0
            }]

        with open(map_path, 'w') as f:
            json.dump(data, f, indent=4)



    
    def update_group_members(self, email, group, membrs_num):
        data = {}
        if os.path.exists(map_path):
            with open(map_path, 'r') as f:
                data = json.load(f)

        if email in data:
            for g in data[email]:
                if g["name"].lower() == group.lower():
                    g["members_num"] = str(membrs_num)
                    break
            else:
                self.logger.info(f"### Group {group} not found for email {email} ###")
        else:
            self.logger.info(f"### Email {email} not found ###")

        with open(map_path, 'w') as f:
            json.dump(data, f, indent=4)


    def get_groups_map(self,email):
        data = {}
        
        if os.path.exists(map_path):
            with open(map_path, 'r') as f:
                data = json.load(f)

        return data.get(email, None)
    


    def get_groups(self,email):
        if os.path.exists(map_path):
            with open(map_path, 'r') as f:
                data = json.load(f)
                groups = data.get(email, [])
                return [group['name'] for group in groups if 'name' in group]
        else:
            return None

    

    def remove_group(email, group):
        data = {}

        if os.path.exists("GroupsMap.json"):
            with open("GroupsMap.json", 'r') as f:
                data = json.load(f)

        if email in data and group in data[email]:
            data[email].remove(group)

            if not data[email]:
                del data[email]

            with open("GroupsMap.json", 'w') as f:
                json.dump(data, f)


    def update_email_status(self, email, status):
        with open(gmail_map_file, 'r') as f:
            data = json.load(f)

        for item in data:
            if item['email'] == email:
                item['status'] = status
                break

        with open(gmail_map_file, 'w') as f:
            json.dump(data, f, indent=4)


    def update_email_pass(self, email, password):
        with open(gmail_map_file, 'r') as f:
            data = json.load(f)

        for item in data:
            if item['email'] == email:
                item['password'] = password
                break

        with open(gmail_map_file, 'w') as f:
            json.dump(data, f, indent=4)


    def check_js(self,elem):
        var_js = f"return document.body.textContent.includes({elem})"
        try:
            found = self.browser.execute_js(var_js)
        except:
            found = False
        if found == "True":
            return True
        else:
            return False


    def CaptchaVerif(self):
        for xpath in cp_xpaths:
            try:
                self.browser.find_xpath(xpath)
                self.logger.error(f"Captcha Detected with XPath: {xpath}")
                return True
            except NoSuchElementException:
                continue
        return False


    def solve_captcha(self):
        api_key = "d315b270071ccc3922a75b7c56e72da1"
        solver = TwoCaptcha(api_key)
        try:
            result = solver.recaptcha(
                sitekey='6LctgAgUAAAAACsC7CsLr_jgOWQ2ul2vC_ndi8o2',
                url='https://groups.google.com/my-groups?hl=fr-FR')

        except Exception as e:
            self.logger.error(f"{str(e)}")

        else:
            #print('solved: ' + str(result))
            #print("Captcha Solved!!")
            return result['code']

   
    def CaptchaSolver(self):
        token = self.solve_captcha()
        self.callback = self.browser.get_callback()
        try:
            cap_id = self.browser.execute_js(""" return document.querySelector('[name="g-recaptcha-response"]').id """)
        except:
            cap_id = None
        if self.callback is not None and cap_id is not None:
            try:
                self.browser.execute_js(f"""document.querySelector('#{cap_id}').innerText='{token}'; {self.callback}.call()""")
                self.logger.info("### captcha Submited!! ###")
            except:
                #print("Didn't work!!")
                self.logger.error(f"+++ Can't Solve Captcha!! +++")
                #self.terminate_selenium_driver()




    def passEmail(self):
        email_field = self.browser.wait_xpath_presence('//input[@type="email"]')
        try:
            email_field.send_keys(self.browser.email)
        except:
            try:
                self.browser.find_xpath('#identifierId').send_keys(self.browser.email)
            except:
                try:
                    self.browser.find_css('#identifierId').send_keys(self.browser.email)
                except:
                    try:
                        self.browser.execute_js(f'document.getElementsByName("identifier")[0].value = "{self.browser.email}"')
                    except:
                        pass

        try:
            self.browser.find_css('#identifierNext > div > button').click()
        except:
            try:
                self.browser.find_xpath("//button[contains(., 'Suivant')]").click()
            except:
                try:
                    self.browser.find_xpath('//*[@id="identifierNext"]/div/button').click()
                except:
                    try:
                        self.browser.execute_js(""" document.querySelector('[id="identifierNext"]').click() """)
                    except:
                        self.browser.find_xpath('//input[@type="email"]').send_keys(Keys.ENTER)


    def passPassword(self):
        password_field = self.browser.wait_css_clickable("""input[type='password']""")
        try:
            password_field.send_keys(self.browser.password)
        except:
            try:
                self.browser.find_css('#password > div.aCsJod.oJeWuf > div > div.Xb9hP > input').send_keys(self.browser.password)
            except:
                try:
                    self.browser.find_xpath('//input[@aria-label="Saisissez votre mot de passe"]').send_keys(self.browser.password)
                except:
                    try:
                        self.browser.execute_js(f'document.getElementsByName("Passwd")[0].value = "{self.browser.password}"')
                    except:
                        pass

        sleep(0.8)

        try:
            self.browser.find_xpath('//*[@id="passwordNext"]/div/button').click()
        except:
            try:
                self.browser.find_css('#passwordNext > div > button').click()
            except:
                try:
                    self.browser.execute_js(""" document.querySelector('[id="passwordNext"]').click() """)
                except:
                    password_field.send_keys(Keys.ENTER)



    def signchooser(self):
        try:
            self.browser.find_xpath('//*[@id="view_container"]/div/div/div[2]/div/div[1]/div/form/span/section/div/div/div/div/ul/li[1]/div').click()
        except:
            self.browser.find_css('#view_container > div > div > div.pwWryf.bxPAYd > div > div.WEQkZc > div > form > span > section > div > div > div > div > ul > li.JDAKTe.ibdqA.W7Aapd.zpCp3.SmR8 > div').click()
        sleep(1.3)
        
        self.passPassword()

    


    def webreauth(self):
        try:
            try:
                self.browser.find_css('#identifierNext > div > button > span').click()
            except:
                try:
                    self.browser.find_xpath("//button[@type='button' and  contains(., 'Suivant') ]").click()
                except:
                    try:
                        self.browser.find_xpath('//*[@id="identifierNext"]/div/button').click()
                    except:
                        raise RuntimeError("Can't Find Next Button!!")
            
            sleep(3)
            
            self.passPassword()
        
        except Exception as e:
            self.logger.error(f"{str(e)}")
            self.terminate_selenium_driver()


    def rejected(self):
        try:
            try:
                self.browser.find_xpath('//*[@id="accountRecoveryButton"]').click()
            except:
                try:
                    self.browser.find_css('#accountRecoveryButton').click()
                except:
                    try:
                        self.browser.execute_js(""" document.querySelector('[aria-label="Continuer"]').click() """)
                    except:
                        raise RuntimeError("Can't Find Recovery Button!!")
        
            sleep(2.5)
            
            try:
                self.browser.find_xpath('//*[@id="identifierNext"]/div/button').click()
            except:
                try:
                    self.browser.find_css('#identifierNext > div > button').click()
                except:
                    try:
                        self.browser.execute_js(""" document.querySelector('[type="button"]').click() """)
                    except:
                        raise RuntimeError("Can't Find Next Button!!")
                        
            sleep(2.2)

            self.passPassword()
            
            sleep(2.5)

            try:
                self.browser.find_xpath('//*[@id="knowledgePreregisteredEmailInput"]').send_keys(self.conf)
            except:
                try:
                    self.browser.find_css('#knowledgePreregisteredEmailInput').send_keys(self.conf)
                except:
                    try:
                        self.browser.execute_js(f""" document.querySelector('[type="email"]').value = "{self.conf}" """)
                    except:
                        raise RuntimeError("Can't Find Email Input!!")
        except Exception as e:
            self.logger.error(f"{str(e)}")
            self.terminate_selenium_driver()


    def phoneVerif(self):
        self.logger.info(f"Checking Phone Recovery!!")
        try: 
            phone_recov = self.browser.find_xpath("//*[contains(text(),'Obtenir un code de validation')]")
            self.logger.info(f"Phone Recovery Detected!!")
            self.logger.info(f"{phone_recov}")
        except:
            phone_recov = False
        if "signin/challenge/iap" in self.browser.this_url() or phone_recov:
            #self.update_email_status(self.browser.email, "phone")
            self.logger.info(f"Phone Recovery >> True")
            self.wait_for_verification()
            phone_recov = True
        else:
            try:
                self.browser.find_xpath("//*[contains(text(),'Ne plus me demander sur cet appareil')]").click()
                #self.update_email_status(self.browser.email, "phone")
                phone_recov = True
            except Exception:
                pass

        self.logger.info(f"Phone Recovery >> {phone_recov}")



    def change_password(self):
        self.new_pass = f"@{self.browser.password}@"

        try:
            self.browser.wait_xpath_presence('//*[@id="passwd"]/div[1]/div/div[1]/input')
        except:
            self.browser.wait_xpath_presence('//*[@id="Password"]')
        try:
            self.browser.find_xpath('//*[@id="passwd"]/div[1]/div/div[1]/input').send_keys(self.new_pass)
        except:
            self.browser.find_xpath('//*[@id="Password"]').send_keys(self.new_pass)
        sleep(0.5)
        try:
            self.browser.find_xpath('//*[@id="confirm-passwd"]/div[1]/div/div[1]/input').send_keys(self.new_pass)
            self.browser.find_xpath('//*[@id="confirm-passwd"]/div[1]/div/div[1]/input').send_keys(Keys.ENTER)
        except:
            self.browser.find_xpath('//*[@id="ConfirmPassword"]').send_keys(self.new_pass)
            self.browser.find_xpath('//*[@id="ConfirmPassword"]').send_keys(Keys.ENTER)
        
        self.update_email_pass(self.browser.email, self.new_pass)
        self.logger.info(f"### Password Changed!! ###")


    def login(self):
        #### &hl=fr-FR ####
        #sleep(3)

        self.passEmail()
        
        sleep(2)

        if self.CaptchaVerif():
            self.CaptchaSolver()

        sleep(2)

        self.passPassword()

        sleep(3)

        try:
            if self.CaptchaVerif():
                self.CaptchaSolver()
        except:
            pass
        
        sleep(1)

        try:
            toggle_button = self.browser.wait_css_clickable("#confirm")
            toggle_button.click()
        except TimeoutException:
            self.logger.info("No 'I understand' button found")

        self.phoneVerif()

        sleep(1.5)

        if"speedbump/changepassword" in self.browser.this_url():
            self.change_password()

    

    def export_ck_lc(self):
        self.browser.go("https://google.com")
        cookies = self.browser.get_cookies()
        cookies_path = os.path.join(home, "Cookies", self.browser.email)
        
        if not os.path.exists(cookies_path):
            os.makedirs(cookies_path)
        
        with open(os.path.join(cookies_path, 'cookies.json'), 'w') as f:
            json.dump(cookies, f, indent=4)
        
        local_storage = self.browser.execute_js(
            "let ls = {}; for (let i = 0; i < localStorage.length; i++) { " 
            "let key = localStorage.key(i); ls[key] = localStorage.getItem(key); } return ls;"
        )
        
        with open(os.path.join(cookies_path, 'localStorage.json'), 'w') as f:
            json.dump(local_storage, f, indent=4)
        
        self.logger.info("### Cookies and Local Storage Exported!! ###")
        return


    def fix_errors(self):
        print("Fixing Errors!!")
        
        while self.browser.running() == True:
            sleep(2.5)



    def check_groups(self):
        self.browser.go("https://groups.google.com/my-groups?hl=fr-FR")
        self.browser.wait_xpath_presence('//span[contains(., "Créer un groupe")]')
        try:
            groups_list = self.browser.find_xpath_all('//div[@data-rowid]/div')
            if groups_list :
                try:
                    self.add_group(self.browser.email, self.get_group_name())
                except:
                    pass
                return True
            else:
                return False
        except:
            return False

    

    def get_group_name(self):
        #self.login_Wait()
        try:
            grp_name = self.browser.execute_js('return document.querySelectorAll("#yDmH0d > c-wiz.zQTmif.SSPGKf.eejsDc > c-wiz > div > div.U3yzR > div > div.ReSblb.OcVpRe.eFM3be > div:nth-child(2) > div:nth-child(1) > div")[0].attributes[2].value')
        except Exception as e:
            self.logger.error(f"JavaScript execution failed: {e}")
            try:
                grp_name = self.browser.find_xpath_all('//div[@data-rowid]/div')[0].get_attribute("data-group-name")
            except Exception as e:
                self.logger.error(f"XPath execution failed: {e}")
                grp_name = None

        return grp_name


    def get_members_num(self):
        try:
            grp_num = self.browser.execute_js('document.querySelectorAll("#yDmH0d > c-wiz:nth-child(19) > div > div.PgZlod > c-wiz > div > div.GeR1W.xaq4Kc > div > html-blob > div > div")[0].textContent')
            grp_num = grp_num.replace(" membres","").replace(" membre","")
        except:
            try:
                grp_num = self.browser.find_css('#yDmH0d > c-wiz:nth-child(19) > div > div.PgZlod > c-wiz > div > div.GeR1W.xaq4Kc > div > html-blob > div > div').text
                grp_num = grp_num.replace(" membres","").replace(" membre","")
            except:
                grp_num = 0
        return grp_num




    def get_members_js(self):
        dig = ''.join(choice(digits) for _ in range(5))
        grp_num = f"grpnum{dig}"
        func = """
        function getnum() {
        mbr = []
        Node_list = document.getElementsByTagName('div')
        for (let i = 0; i < Node_list.length; i++) {
        if (Node_list[i].innerHTML.indexOf("&nbsp;membre") !== -1){
            mbr.push(Node_list[i])
        }
        }
        return  mbr.at(-1).textContent;
        }
        getnum()
        var newDiv = document.createElement("div");
        var newContent = document.createTextNode(getnum());
        newDiv.appendChild(newContent);
        var currentDiv = document.getElementById('div1');
        document.body.insertBefore(newDiv, currentDiv);
        newDiv.setAttribute('id','%s')
        """ %(grp_num)
        self.browser.execute_js(func)
        num = self.browser.find_css(f"#{grp_num}").text
        return num.replace(" membres","").replace(" membre","")



    def accpet_invite(self):
        try:
            self.browser.find_xpath('//div[@aria-label="Ajouter les membres directement. Si vous désactivez cette option, les membres recevront une invitation."]').click()
        except:
            try:
                self.browser.find_xpath('//*[@id="yDmH0d"]/div[4]/div/div[2]/span/c-wiz/div/div[2]/section[1]/span/div[1]/div/div[5]/div[1]').click()
            except:
                try:
                    self.browser.find_css('#yDmH0d > div.NBxL9e.iWO5td > div > div.I7OXgf.PdrZre.ZEeHrd.fNxzgd.VhQQpd.Niudaf.Inn9w.iWO5td > span > c-wiz > div > div.R1f3ob.iiSk4c > section.gXHEcc.VCDHL.qs41qe > span > div:nth-child(2) > div > div.UY6sJb > div.LsSwGf.SWVgue.br5iZc').click()
                except:
                    self.logger.error(f"Can't Find Send Invitations Button!! [Accept_invite]]")



    def pass_members(self):
        captcha_failed = False
        try:
            self.browser.find_xpath('//*[@id="yDmH0d"]/div[4]/div/div[2]/span/c-wiz/div/div[2]/section[1]/span/div[2]/div[2]/span/span').click()
        except:
            try:
                self.browser.execute_js(
                """Array.prototype.slice.call(document.querySelectorAll('span')) .filter(function (el) { return el.textContent === 'Ajouter' })[21].click(); """)
            except:
                self.logger.error(f"Can't Find Send Invitations Button!! [pass_members[1]]")
        
        
        sleep(uniform(1.4,2.2))


        try:
            self.CaptchaSolver()
        except Exception as e:
            self.logger.error(f"+++ Captcha Solver {str(e)} +++")
            captcha_failed = True
            #self.terminate_selenium_driver()
            #return
        
        if captcha_failed == False:
            sleep(uniform(0.5,1.5))
            
            try:
                self.browser.execute_js(
                    """ Array.prototype.slice.call(document.querySelectorAll('span')) .filter(function (el) { return el.textContent === 'Ajouter' })[23].click(); """)
            except:
                try:
                    self.browser.find_xpath('//*[@id="yDmH0d"]/div[4]/div/div[2]/span/c-wiz/div/div[2]/section[2]/span/div/div[2]/div[1]/span/span').click()
                except:
                    try:
                        self.browser.execute_js("Array.prototype.slice.call(document.querySelectorAll('span')) .filter(function (el) { return el.textContent === 'Ajouter' })[23].click();")
                    except:
                        self.logger.error(f"Can't Find Send Invitations Button!! [pass_members[2]]")
                        #self.browser.finish()
                        #self.terminate_selenium_driver()
                        #return
                



    def get_emails(self):
        files = [f for f in os.listdir(data_directory) if os.path.isfile(os.path.join(data_directory, f))]

        files.sort(key=lambda f: int(f.split('_')[1].split('.')[0]))

        with open(os.path.join(data_directory, files[0]), 'r') as file:
            lines = [line.strip() for line in file.readlines()]

        return lines, files[0]
    


    def update_group_list(self, grp_name):
        # Placeholder method - currently not implemented
        _ = grp_name  # Suppress unused parameter warning
        return
    


    def get_group_admins(self, email, group):
        data = {}
        if os.path.exists(map_path):
            with open(map_path, 'r') as f:
                data = json.load(f)

        if email in data:
            for g in data[email]:
                if g["name"] == group:
                    admins = g.get("admins", "No admins found")
                    return admins.split(",") if admins else []

        return None


    def recovery_file(self,file_name,emails):
        with open(os.path.join(data_directory, file_name), 'w') as file:
            for email in emails:
                file.write(f"{email}\n")


    def delete_file(self,file_path):
        if os.path.exists(os.path.join(data_directory,file_path)):
            os.remove(os.path.join(data_directory,file_path))
            self.logger.info(f"### File {file_path} deleted successfully ###")
        else:
            self.logger.error("+++ The file does not exist +++")


    def create_accounts_map(self):
        gmail_accounts_map = []
        if os.path.exists(gmail_map_file):
            with open(gmail_map_file, 'r') as json_file:
                try:
                    existing_data = json.load(json_file)
                    if existing_data:
                        self.logger.info("Accounts already exist in gmail_map_file. Skipping generation.")
                        return
                except json.JSONDecodeError:
                    pass


        with open(ua_map, 'r') as ua_file:
            ua_list = json.load(ua_file)
        
        with open(gmail_account_file, 'r') as file:
            for line in file:
                try:
                    parts = line.strip().split(':')
                except:
                    parts = line.strip().split(';')
                if len(parts) == 3:
                    email, password, conf = parts
                else:
                    email, password = parts
                gmail_account = {
                    'email': email,
                    'password': password,
                    'ua': choice(ua_list),
                    "email_conf": conf if "@" in conf else "",
                    "phone": conf if "@" not in conf else "",
                    'status': "null"
                }
                gmail_accounts_map.append(gmail_account)

        with open(gmail_map_file, 'w') as json_file:
            json.dump(gmail_accounts_map, json_file, indent=4)


    def create_data_parts(self,cmd=None):
        if not os.path.exists(data_directory):
            os.makedirs(data_directory)

        existing_files = [
            f for f in os.listdir(data_directory)
            if os.path.isfile(os.path.join(data_directory, f)) and re.match(r'data_(\d+)\.txt$', f)
        ]

        if cmd is not None:
            if existing_files:
                existing_numbers = [
                    int(re.findall(r'data_(\d+)\.txt', f)[0]) for f in existing_files
                ]
                max_number = max(existing_numbers)
            else:
                max_number = 0

            with open(data_file, 'r') as file:
                lines = file.readlines()

            start_line = max_number * 50
            total_lines = len(lines)

            if start_line < total_lines:
                for i in range(start_line, total_lines, 50):
                    part_number = (i // 50) + 1
                    part_file_path = os.path.join(data_directory, f"data_{part_number}.txt")
                    with open(part_file_path, 'w') as part_file:
                        part_file.writelines(lines[i:i+50])
                self.logger.info(f"Generated data files from {max_number + 1} to {part_number}")
            else:
                self.logger.info("All data files are up to date.")
        else:
            if not existing_files:
                with open(data_file, 'r') as file:
                    lines = file.readlines()
                    for i in range(0, len(lines), 50):
                        part_file_path = os.path.join(data_directory, f"data_{i//50 + 1}.txt")
                        with open(part_file_path, 'w') as part_file:
                            part_file.writelines(lines[i:i+50])
            else:
                self.logger.info("### Data parts already exist. Skipping generation. ###")
                total_files = len(existing_files)
                self.logger.info(f"### Data Files N: {total_files * 50} ###")
            



    def create_groups(self):
        failed = False
        city_name = CityName().name()
        self.grp_name = f"{city_name}{randint(1000000,9999999)}"
        try:
            self.browser.find_xpath("//span[text()='Créer un groupe']").click()
        except:
            try:
                self.browser.find_xpath('//*[@id="yDmH0d"]/c-wiz[1]/div/div/gm-coplanar-drawer/div/div/span/div/div/div/div[1]/div/button').click()
            except:
                try:
                    self.browser.execute_js("""
                        Array.prototype.slice.call(document.querySelectorAll('span')) .filter(function (el) { return el.textContent === 'Créer un groupe' })[0].click();                   
                                    """)
                except:
                    try:
                        self.browser.find_xpath("//button/span[text()='Créer un groupe']").click()
                    except:
                        self.logger.error("Can't Find Create Group Button!!")
                        failed = True
        sleep(1)
        if not failed:
            sleep(1.2)
            #Select regular Groups
            try:
                self.browser.find_xpath("//div[@data-value='googlegroups.com']").click()
            except:
                try:
                    self.browser.execute_js(""" document.querySelector("div[data-value='googlegroups.com']").click(); """)
                except Exception as e:
                    self.logger.error(f"{str(e)}")
            sleep(0.05)

            try:
                self.browser.find_xpath_all("//span[text()='@googlegroups.com']")[1].click()
            except:
                 self.browser.execute_js("""
                    Array.prototype.slice.call(document.querySelectorAll('span'))
                        .filter(function (el) { return el.textContent === '@googlegroups.com' })[1].click();
                """)
            sleep(0.5)
            #* Group Name
            try:
                self.browser.find_css("#yDmH0d > div.NBxL9e.iWO5td > div > div.I7OXgf.PdrZre.ZEeHrd.VhQQpd.Inn9w.iWO5td > span > c-wiz > div > div > div.R1f3ob.iiSk4c > section.gXHEcc.VCDHL.NcKcee.qs41qe > span > div > div.nRiLA > div.X9qMYb > div > div > div > div.n9IS1.oJeWuf > div.FtBNWb > input").send_keys(self.grp_name)
            except:
                try:
                    self.browser.find_xpath("//input[@aria-label='Nom du groupe']").send_keys(self.grp_name)
                except:
                    try:
                        self.browser.execute_js(f""" document.querySelector('[aria-label="Nom du groupe"]').value = "{self.grp_name}")""" )
                        self.browser.execute_js(f""" document.querySelector('[aria-label="Préfixe d\'adresse e-mail du groupe"]').value = "{self.grp_name.lower()}" """)
                    except:
                        try:
                            self.browser.find_xpath('//*[@id="yDmH0d"]/div[4]/div/div[2]/span/c-wiz/div/div/div[2]/section[1]/span/div/div[2]/div[2]/div/div/div/div[1]/div[1]/input').send_keys(self.grp_name)
                        except:
                            pass
            sleep(0.8)
            try:
                self.browser.find_css("#yDmH0d > div.NBxL9e.iWO5td > div > div.I7OXgf.PdrZre.ZEeHrd.VhQQpd.Inn9w.iWO5td > div.OE6hId.J9fJmf > div:nth-child(1) > span > span").click()
            except:
                try:
                    self.browser.execute_js("""
                    Array.prototype.slice.call(document.querySelectorAll('span')) .filter(function (el) { return el.textContent === 'Suivant' })[3].click();                    
                                    """)  
                except:
                    try:
                        self.browser.find_xpath('//*[@id="yDmH0d"]/div[4]/div/div[2]/div[3]/div[1]/span/span').click()
                    except:
                        pass
            sleep(1.3)

            #Publier Les Messages:
            try:
                self.browser.find_css("#yDmH0d > div.NBxL9e.iWO5td > div > div.I7OXgf.PdrZre.ZEeHrd.VhQQpd.Inn9w.iWO5td > span > c-wiz > div > div > div.R1f3ob.iiSk4c > section.gXHEcc.VCDHL.NcKcee.qs41qe > span > div > div.kzQLre > div:nth-child(4) > div.J6Z1mf > div.yEhNJc > div.xk0jZ.AjVdrc.x9Ufpf").click()
            except:
                try:
                    self.browser.find_xpath('//*[@id="yDmH0d"]/div[4]/div/div[2]/span/c-wiz/div/div/div[2]/section[2]/span/div/div[2]/div[4]/div[2]/div[1]/div[2]').click()
                except:
                    pass
            sleep(0.5)

            # Afficher Liste Membres:
            try:
                self.browser.find_css("#yDmH0d > div.NBxL9e.iWO5td > div > div.I7OXgf.PdrZre.ZEeHrd.VhQQpd.Inn9w.iWO5td > span > c-wiz > div > div > div.R1f3ob.iiSk4c > section.gXHEcc.VCDHL.NcKcee.qs41qe > span > div > div.kzQLre > div:nth-child(5) > div.J6Z1mf > div.yEhNJc > div.xk0jZ.AjVdrc.x9Ufpf.qnnXGd").click()
            except:
                try:
                    self.browser.find_xpath('//*[@id="yDmH0d"]/div[4]/div/div[2]/span/c-wiz/div/div/div[2]/section[2]/span/div/div[2]/div[5]/div[2]/div[1]/div[2]').click()
                except:
                    pass

            sleep(0.5)

            try:
                self.browser.find_css("#yDmH0d > div.NBxL9e.iWO5td > div > div.I7OXgf.PdrZre.ZEeHrd.VhQQpd.Inn9w.iWO5td > div.OE6hId.J9fJmf > div:nth-child(4) > span > span").click()
            except:
                try:
                    self.browser.execute_js("""
                    Array.prototype.slice.call(document.querySelectorAll('span')) .filter(function (el) { return el.textContent === 'Suivant' })[3].click();                    
                                    """)
                except:
                    try:
                        self.browser.find_xpath('//*[@id="yDmH0d"]/div[4]/div/div[2]/div[3]/div[1]/span/span').click()
                    except:
                        pass 

            
            sleep(0.7)

            """
            if admins is not None:

                self.accpet_invite()

                sleep(0.7)

                list_admins = admins.split(",")
                for admin in list_admins:
                    sleep(uniform(0.1,0.3))
                    try:
                        self.browser.find_xpath("//input[@aria-label='Gestionnaires du groupe']").send_keys(admin.strip())
                    except:
                        self.browser.find_xpath("(//input[@spellcheck='false'])[2]").send_keys(admin.strip())
                    sleep(uniform(0.1,0.2))
                    try:
                        self.browser.find_xpath("//input[@aria-label='Gestionnaires du groupe']").send_keys(Keys.RETURN)
                    except:
                        self.browser.find_xpath("(//input[@spellcheck='false'])[2]").send_keys(Keys.RETURN)

                sleep(0.5)
            """

            try:
                self.browser.find_css('#yDmH0d > div.NBxL9e.iWO5td > div > div.I7OXgf.PdrZre.ZEeHrd.VhQQpd.Inn9w.iWO5td > div.OE6hId.J9fJmf > div:nth-child(6) > span > span').click()
            except:
                try:
                    self.browser.find_xpath("//div[@role='button'][./span/span[text()='Créer un groupe']])[3]").click()
                except:
                    try:
                        self.browser.execute_js("""
                        Array.prototype.slice.call(document.querySelectorAll('div')) .filter(function (el) { return el.textContent === 'Créer un groupe' })[4].click()                   
                                        """)
                    except:
                        try:
                            self.browser.find_xpath('//*[@id="yDmH0d"]/div[4]/div/div[2]/div[3]/div[6]/span/span').click()
                        except:
                            self.browser.finish()
                            self.terminate_selenium_driver()
                            self.logger.error(f"Can't Find Create Group Button!!")
                            return


            sleep(2)
            try:
                self.CaptchaSolver()
            except Exception as e:
                self.logger.error(f"{str(e)}")
                #self.terminate_selenium_driver()


            sleep(1.5)

            try:
                self.browser.execute_js("""
                Array.prototype.slice.call(document.querySelectorAll('span')) .filter(function (el) { return el.textContent === 'Créer un groupe' })[3].click();""")
            except:
                try:
                    self.browser.find_xpath("//div[@aria-label='Créer un groupe'][./span/span[text()='Créer un groupe']]").click()
                except:
                    self.logger.error(f"Can't Find Create Group Button After Captcha!!")
                    #self.browser.finish()
                    #self.terminate_selenium_driver()

            sleep(1.5)

            retry_count = 0
            max_retries = 3

            while retry_count < max_retries:
                sleep(uniform(5.5, 7.5))
                if self.check_groups():
                    self.add_group(self.browser.email, self.grp_name.lower())
                    self.logger.info(f"Group {self.grp_name} Created Successfully!!")
                    break
                self.logger.info(f"Retry {retry_count + 1}: Group Creation Failed!!")
                retry_count += 1

                if retry_count == max_retries:
                    self.logger.error(f"Failed to create group after {max_retries} retries.")
                    with open("DeadAccounts.txt", "a") as file:
                        file.write(f"{self.browser.email}\n")
            



    def upload(self):
        self.grp_name = self.get_groups(self.browser.email)[0]
        self.grp_url =  f"https://groups.google.com/g/{self.grp_name}"
        self.browser.go(self.grp_url + "/members?hl=fr-FR")
        self.uploaded_data = []
        #self.i = 0
        error_404 = self.check_js("L'URL demandée est introuvable sur ce serveur. C'est tout")
        error_404 = False
        if error_404 is True:
            self.remove_group(self.browser.email,self.grp_name)
        else:
            try:
                self.first_members_num = self.get_members_js()
                self.update_group_members(self.browser.email,self.grp_name.lower(),self.first_members_num)
            except:
                try:
                    self.first_members_num = self.get_members_num()
                    self.update_group_members(self.browser.email,self.grp_name.lower(),self.first_members_num)
                except:
                    self.first_members_num = 0
            self.logger.info(f"### Group Members: {self.first_members_num} ###")

            try:
                self.emails, file_name = self.get_emails()
            except Exception as e:
                self.logger.error(f"{str(e)}")
                self.browser.finish()
                self.terminate_selenium_driver()
                self.logger.error("+++ Can't get data!! +++")
                return
            
            try:
                self.browser.wait_xpath_presence('//div[@aria-label="Ajouter"]/span/span[contains(., "Ajouter")]')
            except:
                self.browser.wait_css_clickable("#yDmH0d > c-wiz:nth-child(19) > div > div.PgZlod > c-wiz > div > div.GeR1W.xaq4Kc > div > html-blob > div > span > div:nth-child(1) > span > span")

            try:
                self.browser.find_xpath('//div[@aria-label="Ajouter"]/span/span[contains(., "Ajouter")]').click()
            except Exception as e:
                self.logger.error(f"{str(e)}")
                return

            sleep(uniform(0.5,1.5))


            try:
                self.browser.find_xpath('//div[@aria-label="Ajouter les membres directement. Si vous désactivez cette option, les membres recevront une invitation."]').click()
            except:
                try:
                    self.browser.find_xpath('//*[@id="yDmH0d"]/div[4]/div/div[2]/span/c-wiz/div/div[2]/section[1]/span/div[1]/div/div[5]/div[1]').click()
                except:
                    try:
                        self.browser.find_css('#yDmH0d > div.NBxL9e.iWO5td > div > div.I7OXgf.PdrZre.ZEeHrd.fNxzgd.VhQQpd.Niudaf.Inn9w.iWO5td > span > c-wiz > div > div.R1f3ob.iiSk4c > section.gXHEcc.VCDHL.qs41qe > span > div:nth-child(2) > div > div.UY6sJb > div.LsSwGf.SWVgue.br5iZc').click()
                    except:
                        self.logger.error(f"+++ Can't Find Ajouter Directement Button!! +++")
                        
            self.logger.info(f"### Uploading  {len(self.emails)} emails to Group: {self.grp_name} ###")
            for email in self.emails:
                sleep(uniform(0.1,0.3))
                try:
                    self.browser.find_xpath("//input[@aria-label='Membres du groupe']").send_keys(email)
                except:
                    self.browser.find_xpath("//input[@spellcheck='false']").send_keys(email)
                self.uploaded_data.append(email)
                sleep(uniform(0.01,0.05))
                try:
                    self.browser.find_xpath("//input[@aria-label='Membres du groupe']").send_keys(Keys.RETURN)
                except:
                    self.browser.find_xpath("//input[@spellcheck='false']").send_keys(Keys.RETURN)
                    
            self.pass_members()

            sleep(uniform(8.5,9.5))

            quota_reached = self.check_js("Échec de l'ajout des membres, car vous avez dépassé votre limite quotidienne. Veuillez réessayer plus tard.")
            self.logger.info(f"### Quota Reached: {quota_reached} ###")

            self.uploaded_sum = len(self.uploaded_data)
            retry_count = 0
            max_retries = 3

            if quota_reached == False:
                sleep(uniform(10.5, 11.5))
                while retry_count < max_retries:
                    self.browser.f5()
                    sleep(uniform(5.5, 7.5))
                    try:
                        self.finish_num_mem = self.get_members_js()
                    except:
                        try:
                            self.finish_num_mem = self.get_members_num()
                        except:
                            self.finish_num_mem = "0"
                    self.logger.info(f"Retry {retry_count + 1}: finish_num_mem = {self.finish_num_mem}, first_members_num = {self.first_members_num}")
                    if int(self.finish_num_mem) > int(self.first_members_num):
                        break
                    retry_count += 1
            else:
                sleep(uniform(2.5, 3.5))

            self.logger.info(f"### Group Members: {self.finish_num_mem} ###")


            if int(self.finish_num_mem) > int(self.first_members_num):
                try:
                    self.delete_file(file_name)
                    self.logger.info(f"### Data Uploaded Successfully!! ###")
                except Exception as e:
                    self.logger.error(f"{str(e)}")

            elif int(self.first_members_num) >= int(self.finish_num_mem) or quota_reached:
                self.logger.error("+++ Data Not Uploaded!! +++")
                return
            else:
                return
            self.update_group_members(self.browser.email,self.grp_name.lower(),self.finish_num_mem)
            
        sleep(2.5)



    def run(self):
        need_fix = []
        with open(gmail_map_file, 'r') as file:
            jsn_data = json.load(file)
        if len(jsn_data) == 0:
            self.logger.info("+++ No Accounts Found +++")
        
        if "fix_errors" in self.actions:
            for account in jsn_data:
                email = account["email"]
                with open(map_path, 'r') as f:
                    grp_data = json.load(f)
                    if email not in grp_data:
                        need_fix.append(email)
                    
        self.logger.info(f"### Total Accounts: {len(jsn_data)} ###")
        self.ac = 0
        for account in jsn_data:
            self.ac+=1
            print("\n")
            self.logger.info(f"====== Account : {self.ac} ======")
            self.email = account["email"]
            self.password = account["password"]
            ua_agent = account["ua"]
            status = account["status"]

            try:
                if "reload_profiles" in self.actions:
                    self.remove_profile(self.email)
                self.browser = Driver(self.email,self.password,ua_agent,self.ac)
                try:
                    self.browser.go("https://accounts.google.com/signin")
                    if "reload_profiles" in self.actions:
                        self.browser.go("https://google.com")
                        self.logger.info(f"### Profile: {self.browser.email} ###")
                        try:
                            cookies_path = f"{home}/Cookies/{self.browser.email}/cookies.json"
                            with open(cookies_path, 'r') as f:
                                cookies = json.load(f)
                            for cookie in cookies:
                                try:
                                    self.browser.add_cookie(cookie)
                                except Exception as e:
                                    pass
                            
                            local_storage_path = f"{home}/Cookies/{self.browser.email}/localStorage.json"

                            with open(local_storage_path, 'r') as f:
                                local_storage = json.load(f)
                                if local_storage:
                                    for key, value in local_storage.items():
                                        self.browser.execute_js(f"window.localStorage.setItem('{key}', '{value}');")
                            self.logger.info(f"### Profile Reloaded: {self.browser.email} ###")    
                        except FileNotFoundError as e:
                            self.logger.error(f"+++ File not found: {e.filename} +++")
                        except json.JSONDecodeError as e:
                            self.logger.error(f"+++ Invalid JSON in file: {e.msg} +++")
                        except Exception as e :
                            self.logger.error(f"{str(e)}")
                        
                except Exception as e:
                    if "ERR_PROXY_CONNECTION_FAILED" in str(e) or "net" in str(e) or "ERR_CONNECTION_RESET" in str(e) :
                        self.logger.error(f"{str(e)}")
                        break
                sleep(1)
                if "https://myaccount.google.com/?utm_source=sign_in_no_continue" not in self.browser.this_url() or "fix_errors" in self.actions:
                    if "fix_errors" not in self.actions:
                        if status == "active" or status == "null":
                            """
                            if "?hl=fr-FR" not in self.browser.this_url():
                                self.browser.go(f"{self.browser.this_url()}?hl=fr-FR")
                            """

                            if "signinchooser" in self.browser.this_url():
                                self.signchooser()


                            if "signin/confirmidentifier" in self.browser.this_url():
                                self.webreauth()
                            

                            elif "accounts.google.com/v3/signin/identifier" in self.browser.this_url() and "login" in self.actions:
                                self.login()
                                self.export_ck_lc()
                                self.update_email_status(self.browser.email, "active")       

                            if "Login&Wait" in self.actions:
                                self.login_Wait()
                            
                            else:
                                if self.check_groups() == False:
                                    self.create_groups()
                                else:
                                    self.logger.info(f"Group Already Exist : {self.get_group_name()}")
                                try:
                                    self.upload()
                                except Exception as e:
                                    self.logger.error(f"Upload '1' {str(e)}")
                                

                    else:
                        if self.email in need_fix:
                            self.logger.info(f"+++ Fixing Errors for {self.browser.email} +++")
                            self.fix_errors()
                            #self.update_email_status(self.browser.email, "active")

                else:
                    if self.check_groups() == False:
                        try:
                            self.create_groups()
                        except Exception as e:
                            self.browser.logger.error(str(e))
                    else:
                        self.logger.info(f"Group Already Exist : {self.get_group_name()}")
                    try:
                        self.upload()
                    except Exception as e:
                        self.logger.error(f"Upload '2' {str(e)}")


                self.browser.finish()
                self.terminate_selenium_driver()


            except Exception as e:
                self.logger.error(f"{str(e)}")
                try:
                    self.browser.finish()
                    self.terminate_selenium_driver()
                except Exception as e:
                    pass
                    
        try:
            self.browser.finish()
            self.terminate_selenium_driver()
        except:
            pass



class Main():
    def __init__(self) -> None:
        logging.basicConfig(
            level=logging.INFO,  
            format='[%(asctime)s - %(levelname)s - %(message)s]',
            datefmt='%Y-%m-%d'
        )
        self.logger = logging.getLogger("Main")
        self.logger.info(f"Starting Groups App on User : {os.getlogin()} !!")

        start_time = time.time()

        balance = self.get_balance()
        if balance == "-0.0":
            print("Captcha out of Balance")
        else:
            while True:
                if len(sys.argv) > 1:
                    answ = sys.argv[1]
                else:
                    answ = self.questions()
                if answ == "1":
                    actions = ["login", "create_groups", "upload"]
                    break
                elif answ == "2":
                    actions = ["fix_errors"]
                    break
                elif answ == "3":
                    actions = ["reload_profiles"]
                    break
                elif answ == "4":
                    self.logger.info(f"### Regenerate Data ###")
                    self.create_data_parts("regenerate")
                    print("Press any key to continue...")
                    msvcrt.getch()
                elif answ == "5":
                    self.logger.info(f"### Groups Count : {str(self.count_groups())} ###")
                    print("Press any key to continue...")
                    msvcrt.getch()
                    os.system('cls' if os.name == 'nt' else 'clear')
                elif answ == "6":
                    self.logger.info(f"### Members Count : {str(self.count_members())} ###")
                    print("Press any key to continue...")
                    msvcrt.getch()
                    os.system('cls' if os.name == 'nt' else 'clear')
                elif answ == "7":
                    self.logger.info(f"### {os.getlogin()} Data Count : {str(self.count_data_files())} ###")
                    self.logger.info(f"### {os.getlogin()} Data Files Count : {str(int(self.count_data_files()/50))} ###")
                    return
                elif answ == "8":
                    self.logger.info(f"### Exporting Accounts ###")
                    self.process_dead_accounts()
                    self.logger.info("### Exporting Accounts Done!! ### ")
                    os.system('cls' if os.name == 'nt' else 'clear')
                else:
                    self.logger.info("Invalid choice, please try again.")
                    if len(sys.argv) > 1:
                        sys.exit(1)

        
        self.create_data_parts()
        worker = Worker(actions)
        worker.create_accounts_map()
        worker.run()

        end_time = time.time()
        execution_time = end_time - start_time
        execution_time_hours = execution_time / 3600
        self.logger.info(f"Script execution time: {execution_time_hours:.2f} hours")



    def get_balance(self):
        api_key = "d315b270071ccc3922a75b7c56e72da1"
        solver = TwoCaptcha(api_key)
        try:
            balance = solver.balance()
            balance = float(str(balance)[:4])  
        except Exception as e:
            self.logger.error(f"{str(e)}")
            balance = 0

        return balance

    def count_groups(self):
        with open(map_path, 'r') as file:
            data = json.load(file)
        name_count = 0
        for _, groups in data.items():
            for group in groups:
                if 'name' in group:
                    name_count += 1
        return name_count
    

    def count_data_files(self):
        num_files = len([
            f for f in os.listdir(data_directory)
            if os.path.isfile(os.path.join(data_directory, f))
        ])
        return num_files * 50


    def create_data_parts(self, cmd=None):
        if not os.path.exists(data_directory):
            os.makedirs(data_directory)
    
        existing_files = [
            f for f in os.listdir(data_directory)
            if os.path.isfile(os.path.join(data_directory, f)) and re.match(r'data_(\d+)\.txt$', f)
        ]

        existing_numbers = set(
            int(re.findall(r'data_(\d+)\.txt', f)[0]) for f in existing_files
        )
    
        if cmd is not None:
            if existing_numbers:
                max_number = max(existing_numbers)
            else:
                max_number = 0
    
            with open(data_file, 'r') as file:
                lines = file.readlines()
    
            total_lines = len(lines)
    
            part_number = max_number + 1
            for i in range(0, total_lines, 50):
                part_file_path = os.path.join(data_directory, f"data_{part_number}.txt")
                with open(part_file_path, 'w') as part_file:
                    part_file.writelines(lines[i:i+50])
                part_number += 1
    
            self.logger.info(f"Generated data files from {max_number + 1} to {part_number - 1}")
        else:
            if not existing_files:
                with open(data_file, 'r') as file:
                    lines = file.readlines()
                    for i in range(0, len(lines), 50):
                        part_file_path = os.path.join(data_directory, f"data_{(i // 50) + 1}.txt")
                        with open(part_file_path, 'w') as part_file:
                            part_file.writelines(lines[i:i+50])
                self.logger.info("Generated data parts.")
            else:
                self.logger.info("### Data parts already exist. Skipping generation. ###")
                total_files = len(existing_files)
                self.logger.info(f"### Data Files N: {total_files * 50} ###")



    def count_members(self):
        with open(map_path, 'r') as file:
            data = json.load(file)
        
        total_members = 0
        for _, groups in data.items():
            for group in groups:
                if 'members_num' in group:
                    total_members += int(group['members_num'])
    
        return total_members


    def clean_file(self, email):
        try:
            profile = f"{profile_home}/{email}"
            if os.path.exists(profile):
                shutil.rmtree(profile)
            else:
                self.logger.warning(f"Profile {profile} does not exist.")
        except Exception as e:
            self.logger.error(f"Error removing profile {profile}: {str(e)}")


    def process_dead_accounts(self):
        with open(dead_accounts, 'r') as file:
            dead_emails = {line.strip() for line in file if line.strip()}
        
        with open(gmail_map_file, 'r') as file:
            gmail_accounts_map = json.load(file)

        matching_accounts = []

        if isinstance(gmail_accounts_map, list):
            updated_accounts_map = []
            for account in gmail_accounts_map:
                email = account.get('email')
                if email in dead_emails:
                    matching_accounts.append(f"{email}:{account.get('password')}")
                    self.clean_file(email)
                else:
                    updated_accounts_map.append(account)
            gmail_accounts_map = updated_accounts_map
        elif isinstance(gmail_accounts_map, dict):
            for email, account_info in gmail_accounts_map.items():
                if email in dead_emails:
                    matching_accounts.append(f"{email}:{account_info['password']}")
            gmail_accounts_map = {email: info for email, info in gmail_accounts_map.items() if email not in dead_emails}
        else:
            raise ValueError("Unexpected JSON structure in Gmail File")

        with open(gmail_map_file, 'w') as file:
            json.dump(gmail_accounts_map, file, indent=4)

        with open("recycled_accounts.txt", 'w') as file:
            for account in matching_accounts:
                file.write(f"{account}\n")

        with open(dead_accounts, 'w') as file:
            file.truncate()


    def questions(self):
        if not os.path.exists(settings_path):
            use_proxy = input("settings.json not found. Do you want to use a proxy? (yes/no): ").strip().lower()
            if use_proxy.lower() in ['y', 'yes']:
                if os.path.exists(proxy_file):
                    with open(proxy_file, 'r') as prx_file:
                        proxies = prx_file.readlines()
                    if proxies:
                        proxy = random.choice(proxies).strip()
                        settings = {
                            'use_proxy': True,
                            'proxy': proxy
                        }
                        with open(settings_path, 'w') as settings_file:
                            json.dump(settings, settings_file, indent=4)
                        self.logger.info("Proxy settings saved to settings.json")
                    else:
                        self.logger.error("proxy.txt is empty.")
                else:
                    self.logger.error("proxy.txt not found.")
            else:
                settings = {
                    'use_proxy': False
                }
                with open(settings_path, 'w') as settings_file:
                    json.dump(settings, settings_file, indent=4)
                self.logger.info("Settings saved to settings.json without proxy")

        self.logger.info("### What do you want to do? ###")
        self.logger.info(""" 1. Login & Create _ Upload""")
        self.logger.info(""" 2. Fix Errors : Like change Phone / Update Settings""")
        self.logger.info(""" 3. Reload Profiles""")
        self.logger.info(""" 4. Regenarate Data Parts""")
        self.logger.info(""" 5. Count Groups Number""")
        self.logger.info(""" 6. Count Group Members""")
        self.logger.info(""" 7. Count Data Files""")
        self.logger.info(""" 8. Export DeadAccounts""")
        return input("Please enter your choice: ")

Main()




    


    

    

    


